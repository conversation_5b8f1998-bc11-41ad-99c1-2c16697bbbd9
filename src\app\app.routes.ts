import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./layouts/default-layout/default-layout').then(
        (m) => m.DefaultLayout
      ),
    children: [
      {
        path: 'examples',
        data: { breadcrumb: 'Examples' },
        children: [
          {
            path: 'tailwind-calsses',
            data: { breadcrumb: 'Tailwind Classes' },
            loadComponent: () =>
              import('./examples/tailwind-classes-test/tailwind-classes-test').then(
                (m) => m.TailwindClassesTest
              ),
          },
          {
            path: 'svg-icons',
            data: { breadcrumb: 'SVG Icons' },
            loadComponent: () =>
              import('./examples/svg-icon-test/svg-icon-test').then(
                (m) => m.SvgIconTest
              ),
          },
          {
            path: 'progress-indicators',
            data: { breadcrumb: 'Progress Indicators' },
            loadComponent: () =>
              import('./examples/progress-indicator-test/progress-indicator-test').then(
                (m) => m.ProgressIndicatorTest
              ),
          },
          {
            path: 'inputs',
            data: { breadcrumb: 'Inputs' },
            loadComponent: () =>
              import('./examples/inputs-test/inputs-test').then(
                (m) => m.InputsTest
              ),
          },
          {
            path: 'accordion',
            data: { breadcrumb: 'Accordion' },
            loadComponent: () =>
              import('./examples/accordion-test/accordion-test').then(
                (m) => m.AccordionTest
              ),
          },
          {
            path: 'buttons',
            data: { breadcrumb: 'Buttons' },
            loadComponent: () =>
              import('./examples/buttons-test/buttons-test').then(
                (m) => m.ButtonsTest
              ),
          },
          {
            path: 'content-switcher',
            data: { breadcrumb: 'Content Switcher' },
            loadComponent: () =>
              import('./examples/content-switcher-test/content-switcher-test').then(
                (m) => m.ContentSwitcherTest
              ),
          },
        ],
      },
      {
        path: '',
        redirectTo: '',
        pathMatch: 'full'
      }
    ],
  },
];
