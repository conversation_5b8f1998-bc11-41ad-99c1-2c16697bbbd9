.button {
    @apply 
    rounded-sm 
    flex 
    items-center  
    focus-visible:outline-default-100
    focus-visible:outline-offset-2
}

.button--lg {
    @apply px-4 py-2 justify-between
}

.button--lg--square {
    @apply p-2 justify-center
}

.button--md {
    @apply px-3 py-sm justify-between
}

.button--md--square {
    @apply p-sm justify-center
}

.button--sm {
    @apply px-2 py-sm justify-between
}

.button--sm--square {
    @apply p-1 justify-center
}

///////////////////// Types //////////////////////

.button-neutral {
    @apply
    bg-gray-950 
    text-white
    hover:bg-gray-800
    active:bg-gray-600

    disabled:bg-gray-200
    disabled:text-gray-400
}

.button-neutral.selected {
    @apply bg-green-100
}

/////////////////////////////////////////////////

.button-primary {
    @apply
    bg-sa-600
    text-white
    hover:bg-sa-700
    active:bg-sa-900

    disabled:bg-gray-200
    disabled:text-gray-400
}

.button-primary.selected {
    @apply bg-sa-800
}

/////////////////////////////////////////////////

.button-secondary-solid {
    @apply
    bg-gray-100
    text-default-100
    hover:bg-gray-200
    active:bg-gray-200

    disabled:bg-gray-200
    disabled:text-gray-400
}

.button-secondary-solid.selected {
    @apply bg-gray-200
}

/////////////////////////////////////////////////

.button-secondary-outline {
    @apply
    border
    border-gray-300
    text-default-100
    hover:bg-gray-100
    hover:border
    hover:border-gray-200
    active:bg-gray-200
    active:border
    active:border-gray-300

    disabled:border
    disabled:border-gray-200
    disabled:text-gray-400
}

.button-secondary-outline.selected {
    @apply bg-gray-200
    border
    border-gray-300
}

/////////////////////////////////////////////////

.button-subtle {
    @apply
    text-default-100
    hover:bg-gray-100
    active:bg-gray-200
    
    disabled:text-gray-400
}

.button-subtle.selected {
    @apply bg-gray-200
}

/////////////////////////////////////////////////

.button-transparent {
    @apply
    text-default-100
    hover:text-sa-700
    active:text-sa-900
    
    disabled:text-gray-400
}

.button-transparent.selected {
    @apply text-sa-800
}

/////////////////////////////////////////////////

.button-primary-destructive {
    @apply
    bg-error-600
    text-white
    hover:bg-error-700
    active:bg-error-900
}

.button-secondary-solid-destructive {
    @apply
    bg-error-50
    text-error-700
    hover:bg-error-100
    active:bg-error-200
}

.button-secondary-outline-destructive {
    @apply
    border
    border-error-200
    text-error-700
    hover:bg-error-100
    hover:border-error-200
    active:bg-error-200
    active:border-error-200
}

.button-subtle-destructive {
    @apply
    text-error-700
    hover:bg-error-100
    active:bg-error-200
}

.button-transparent-destructive {
    @apply
    text-error-600
    hover:text-error-700
    active:text-error-900
}


///////////////////// Types on color //////////////////////

.button-neutral-oncolor {
    @apply
    bg-white 
    text-default-100
    hover:bg-opacity-80
    active:bg-opacity-60

    disabled:bg-white
    disabled:bg-opacity-20
    disabled:text-white
    disabled:text-opacity-40
}

.button-neutral-oncolor.selected {
    @apply bg-white bg-opacity-70
}

/////////////////////////////////////////////////

.button-primary-oncolor {
    @apply
    bg-white
    text-default-100
    hover:bg-opacity-80
    active:bg-opacity-60

   disabled:bg-white
    disabled:bg-opacity-20
    disabled:text-white
    disabled:text-opacity-40
}

.button-primary-oncolor.selected {
    @apply bg-white bg-opacity-60
}

/////////////////////////////////////////////////

.button-secondary-solid-oncolor {
    @apply
    bg-white
    bg-opacity-20
    text-white
    active:bg-opacity-40

    disabled:bg-white
    disabled:bg-opacity-20
    disabled:text-white
    disabled:text-opacity-40
}

.button-secondary-solid-oncolor.selected {
    @apply bg-white bg-opacity-30
}

/////////////////////////////////////////////////

.button-secondary-outline-oncolor {
    @apply
    border
    border-white
    border-opacity-40
    text-white
    hover:bg-white
    hover:bg-opacity-20
    active:bg-white
    active:bg-opacity-40

    disabled:border
    disabled:border-white
    disabled:border-opacity-40
    disabled:text-white
    disabled:text-opacity-40
}

.button-secondary-outline-oncolor.selected {
    @apply bg-white
    bg-opacity-30
    border
    border-white
    border-opacity-40
}

/////////////////////////////////////////////////

.button-subtle-oncolor {
    @apply
    text-white
    hover:bg-white
    hover:bg-opacity-20
    active:bg-white
    active:bg-opacity-40
    
    disabled:text-white
    disabled:text-opacity-40
}

.button-subtle-oncolor.selected {
    @apply bg-white
    bg-opacity-30
}

/////////////////////////////////////////////////

.button-transparent-oncolor {
    @apply
    text-white
    hover:text-sa-400
    active:text-sa-300
    
    disabled:text-white
    disabled:text-opacity-40
}

.button-transparent-oncolor.selected {
    @apply text-sa-400
}

/////////////////////////////////////////////////

// .primary-destructive-oncolor {
//     @apply
//     bg-error-600
//     text-white
//     hover:bg-error-700
//     active:bg-error-900
// }

// .secondary-solid-destructive-oncolor {
//     @apply
//     bg-error-50
//     text-error-700
//     hover:bg-error-100
//     active:bg-error-200
// }

.button-secondary-outline-destructive-oncolor {
    @apply
    border
    border-error-200
    text-error-200
    hover:bg-error-100
    hover:border-error-200
    hover:text-error-700
    active:bg-error-200
    active:border-error-200
    active:text-error-700
}

.button-subtle-destructive-oncolor {
    @apply
    text-error-200
    hover:bg-error-100
    hover:text-error-700
    active:bg-error-200
    active:text-error-700
}

.button-transparent-destructive-oncolor {
    @apply
    text-error-200
    hover:text-error-300
    active:text-error-400
}

