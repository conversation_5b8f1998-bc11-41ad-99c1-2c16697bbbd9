///////////////////////////// Labels ////////////////////////////

.label {
    @apply
    rounded-sm
    inline-flex
    items-center
}

.label-rounded {
    @apply 
    rounded-full
    inline-flex
    items-center
}

.label-md {
    @apply px-3 py-1 text-text-md font-medium justify-between
}

.label-md-square {
    @apply p-2 justify-center
}

.label-xs {
    @apply px-2 py-1 text-text-xs font-medium justify-between
}

.label-xs-square {
    @apply p-1 justify-center
}

.label-2xs {
    @apply px-2 py-1 text-text-2xs font-semibold justify-between
}

.label-2xs-square {
    @apply p-1 justify-center
}

//////////////////////// Label types ////////////////////////////

.label-neutral {
    @apply 
    bg-gray-50 
    text-gray-800 
    border
    border-gray-200
}

.label-neutral-outline {
    @apply 
    border
    border-gray-600
    text-gray-800 
}

.label-success{
    @apply 
    bg-info-50 
    text-success-800 
    border
    border-success-200
}

.label-success-outline{
    @apply 
    border
    border-success-700
    text-success-800 
}

.label-error{
    @apply 
    bg-error-50 
    text-error-800 
    border
    border-error-200
}

.label-error-outline{
    @apply 
    border
    border-error-700
    text-error-800
}

.label-warning{
    @apply 
    bg-warning-50 
    text-warning-800 
    border
    border-warning-200
}

.label-warning-outline{
    @apply 
    border
    border-warning-700
    text-warning-800
}

.label-info{
    @apply 
    bg-info-75 
    text-info-800 
    border
    border-info-200
}

.label-info-outline{
    @apply 
    border
    border-info-700
    text-info-800
}

.label-oncolor {
    @apply
    bg-white
    bg-opacity-20
    text-white
}

.label-oncolor-outline {
    @apply
    border
    border-white
    border-opacity-60
    text-white
}


/////////////////////////// Statuses ////////////////////////////

.status {
    @apply
    rounded-full
    inline-flex
    items-center
    font-medium
}

.status-md {
    @apply
    px-4 py-1 text-text-md
}

.status-sm {
    @apply
    px-2 py-xxs text-text-sm
}

.status-xs {
    @apply
    px-2 py-1 text-text-2xs
}

//////////////////////// Status types ////////////////////////////

.status-subtle-neutral {
    @apply
    bg-gray-50
    text-gray-800
}

.status-subtle-blue {
    @apply
    bg-info-75
    text-info-800
}

.status-subtle-green {
    @apply
    bg-success-50
    text-success-800
}

.status-subtle-yellow {
    @apply
    bg-warning-50
    text-warning-800
}

.status-subtle-red {
    @apply
    bg-error-50
    text-error-800
}


.status-inverted-neutral {
    @apply
    bg-gray-600
    text-white
}

.status-inverted-blue {
    @apply
    bg-info-600
    text-white
}

.status-inverted-green {
    @apply
    bg-success-600
    text-white
}

.status-inverted-yellow {
    @apply
    bg-warning-600
    text-white
}

.status-inverted-red {
    @apply
    bg-error-600
    text-white
}

.status-ghost-neutral{
    @apply
    p-0
    text-gray-800
}

.status-ghost-neutral span{
    @apply
    text-gray-600
}

.status-ghost-blue{
    @apply
    p-0
    text-gray-800
}

.status-ghost-blue span{
    @apply
    text-info-800
}

.status-ghost-green{
    @apply
    p-0
    text-gray-800
}

.status-ghost-green span{
    @apply
    text-success-800
}

.status-ghost-yellow{
    @apply
    p-0
    text-gray-800
}

.status-ghost-yellow span{
    @apply
    text-warning-800
}

.status-ghost-red{
    @apply
    p-0
    text-gray-800
}

.status-ghost-red span{
    @apply
    text-error-800
}