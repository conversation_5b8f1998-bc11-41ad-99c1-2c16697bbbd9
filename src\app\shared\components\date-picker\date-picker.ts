import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, OnInit, HostListener, ElementRef } from '@angular/core';

export interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

@Component({
  selector: 'sdga-date-picker',
  imports: [CommonModule],
  templateUrl: './date-picker.html',
  styleUrl: './date-picker.scss'
})
export class DatePicker implements OnInit {
  @Input() selectedDate: Date | null = null;
  @Input() selectedRange: DateRange | null = null;
  @Input() isRange: boolean = false;
  @Input() minDate: Date | null = null;
  @Input() maxDate: Date | null = null;
  @Input() isOpen: boolean = false;
  
  @Output() dateSelected = new EventEmitter<Date>();
  @Output() rangeSelected = new EventEmitter<DateRange>();
  @Output() closed = new EventEmitter<void>();

  currentMonth: Date = new Date();
  today: Date = new Date();
  weeks: Date[][] = [];
  monthNames: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  dayNames: string[] = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
  
  // Range selection state
  rangeStartDate: Date | null = null;
  rangeEndDate: Date | null = null;
  isSelectingRange: boolean = false;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    this.generateCalendar();
    if (this.selectedRange) {
      this.rangeStartDate = this.selectedRange.startDate;
      this.rangeEndDate = this.selectedRange.endDate;
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    if (this.isOpen && !this.elementRef.nativeElement.contains(event.target as Node)) {
      this.closeCalendar();
    }
  }

  generateCalendar() {
    const year = this.currentMonth.getFullYear();
    const month = this.currentMonth.getMonth();
    
    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Start from the first Sunday of the week containing the first day
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());
    
    this.weeks = [];
    let currentDate = new Date(startDate);
    
    // Generate 6 weeks to ensure we cover the entire month
    for (let week = 0; week < 6; week++) {
      const weekDays: Date[] = [];
      for (let day = 0; day < 7; day++) {
        weekDays.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }
      this.weeks.push(weekDays);
    }
  }

  previousMonth() {
    this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, 1);
    this.generateCalendar();
  }

  nextMonth() {
    this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 1);
    this.generateCalendar();
  }

  selectDate(date: Date) {
    if (this.isDateDisabled(date)) {
      return;
    }

    if (this.isRange) {
      this.selectRangeDate(date);
    } else {
      this.selectedDate = new Date(date);
      this.dateSelected.emit(this.selectedDate);
      // Don't close calendar for single date selection - let user click outside to close
    }
  }

  selectRangeDate(date: Date) {
    if (!this.rangeStartDate || (this.rangeStartDate && this.rangeEndDate)) {
      // Start new range selection
      this.rangeStartDate = new Date(date);
      this.rangeEndDate = null;
      this.isSelectingRange = true;
    } else if (this.rangeStartDate && !this.rangeEndDate) {
      // Complete range selection
      if (date >= this.rangeStartDate) {
        this.rangeEndDate = new Date(date);
      } else {
        // If selected date is before start date, swap them
        this.rangeEndDate = this.rangeStartDate;
        this.rangeStartDate = new Date(date);
      }

      this.selectedRange = {
        startDate: this.rangeStartDate,
        endDate: this.rangeEndDate
      };

      this.rangeSelected.emit(this.selectedRange);
      this.isSelectingRange = false;
      // Don't close calendar for range selection - let user click outside to close
    }
  }

  isDateDisabled(date: Date): boolean {
    if (this.minDate && date < this.minDate) {
      return true;
    }
    if (this.maxDate && date > this.maxDate) {
      return true;
    }
    return false;
  }

  isDateSelected(date: Date): boolean {
    if (this.isRange) {
      return this.isDateInRange(date) || this.isRangeEndpoint(date);
    } else {
      return this.selectedDate ? this.isSameDay(date, this.selectedDate) : false;
    }
  }

  isDateInRange(date: Date): boolean {
    if (!this.rangeStartDate || !this.rangeEndDate) {
      return false;
    }
    return date > this.rangeStartDate && date < this.rangeEndDate;
  }

  isRangeEndpoint(date: Date): boolean {
    if (!this.rangeStartDate) {
      return false;
    }
    
    const isStart = this.isSameDay(date, this.rangeStartDate);
    const isEnd = this.rangeEndDate ? this.isSameDay(date, this.rangeEndDate) : false;
    
    return isStart || isEnd;
  }

  isToday(date: Date): boolean {
    return this.isSameDay(date, this.today);
  }

  isCurrentMonth(date: Date): boolean {
    return date.getMonth() === this.currentMonth.getMonth() && 
           date.getFullYear() === this.currentMonth.getFullYear();
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.getDate() === date2.getDate() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getFullYear() === date2.getFullYear();
  }

  closeCalendar() {
    this.isOpen = false;
    this.closed.emit();
  }

  onCalendarClick(event: Event) {
    event.stopPropagation();
  }

  getCurrentMonthYear(): string {
    return `${this.monthNames[this.currentMonth.getMonth()]} ${this.currentMonth.getFullYear()}`;
  }
}
