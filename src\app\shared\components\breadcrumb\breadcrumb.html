<nav aria-label="breadcrumb" class="flex items-center text-md text-default-100 gap-xs">
  <ng-container *ngFor="let crumb of breadcrumbs; let last = last">
    <a [routerLink]="crumb.url" class="hover:underline">{{ crumb.label }}</a>
    <span *ngIf="!last">
      <sdga-svg-icon name="arrow-right" svgClass="ltr:inline rtl:hidden text-gray-400"></sdga-svg-icon>
      <sdga-svg-icon name="arrow-left" svgClass="rtl:inline ltr:hidden text-gray-400"></sdga-svg-icon>
    </span>
  </ng-container>
</nav>
