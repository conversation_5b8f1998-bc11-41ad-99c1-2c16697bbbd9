import { Component } from '@angular/core';
import { SvgIcon } from "../svg-icon/svg-icon";
import { CommonModule } from '@angular/common';

@Component({
  selector: 'sdga-footer',
  imports: [SvgIcon,CommonModule],
  templateUrl: './footer.html',
  styleUrl: './footer.scss'
})
export class Footer {

footerLinks = [
    {
      label: 'Group Label',
      links: ['Footer Link', 'Footer Link', 'Footer Link', 'Footer Link', 'Footer Link'],
    },
    {
      label: 'Group Label',
      links: ['Footer Link', 'Footer Link', 'Footer Link', 'Footer Link', 'Footer Link'],
    },
    {
      label: 'Group Label',
      links: ['Footer Link', 'Footer Link', 'Footer Link', 'Footer Link', 'Footer Link'],
    },
    {
      label: 'Group Label',
      links: ['Footer Link', 'Footer Link', 'Footer Link', 'Footer Link', 'Footer Link'],
    },
    {
      label: 'Group Label',
      links: ['Footer Link', 'Footer Link', 'Footer Link', 'Footer Link', 'Footer Link'],
    },
  ];

  bottomLinks = [
    'Footer Link',
    'Footer Link',
    'Footer Link',
    'Footer Link',
    'Footer Link',
  ];
}
