import { Component, Inject, Input, OnInit } from '@angular/core';
import { SvgIcon } from "../svg-icon/svg-icon";
import { FormGroup, FormsModule } from '@angular/forms';
import { FormInput } from '../inputs/form-input/form-input';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { DOCUMENT } from '@angular/common';

interface MenuItem {
  icon: string;
  label: string;
  checked: boolean;
  switch: boolean;
  badge: string | null;
  badgeClass?: string;
  selected?: boolean;
  arrow?: boolean;
  count?: string | null;
}

interface MenuGroup {
  label: string;
  items: MenuItem[];
}

interface MenuHeader {
  icon: string;
  title: string;
  subtitle?: string;
  closeable?: boolean;
}
@Component({
  selector: 'sdga-menu',
  imports: [SvgIcon, FormsModule, FormInput, TranslateModule, CommonModule],
  templateUrl: './menu.html',
  styleUrl: './menu.scss'
})
export class Menu implements OnInit {
  @Input() menuGroups: MenuGroup[] = [];
  @Input() testInputs: FormGroup | undefined;
  @Input() formSubmitted = false;
  direction = 'ltr';
  switchState1 = false;
  switchState2 = true;
  selectedItem: string | null = null;
   @Input() header: MenuHeader | null = null;
   closed = false;

  constructor(@Inject(DOCUMENT) private document: Document) {}

  ngOnInit(): void {
    this.direction = this.document.documentElement.dir || 'ltr';
    const observer = new MutationObserver(() => {
      this.direction = this.document.documentElement.dir || 'ltr';
    });
    observer.observe(this.document.documentElement, { attributes: true, attributeFilter: ['dir'] });
  }

  toggleSelection(item: string) {
    this.selectedItem = this.selectedItem === item ? null : item;
  }

    closeMenu() {
    this.closed = true;
    console.log('Menu closed');
  }
}
