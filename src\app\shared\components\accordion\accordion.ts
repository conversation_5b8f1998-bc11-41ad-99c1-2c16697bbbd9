import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { SvgIcon } from "../svg-icon/svg-icon";

@Component({
  selector: 'sdga-accordion',
  imports: [CommonModule, SvgIcon],
  templateUrl: './accordion.html',
  styleUrl: './accordion.scss'
})
export class Accordion {
@Input() title!: string;
@Input() accordionClass! :string;
@Input() contentClass! :string;

@Input() isOpen!:boolean;

}
