import { Component } from '@angular/core';
import { Avatar, AvatarData } from '../../shared/components/avatar/avatar';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'sdga-avatar-test',
  imports: [CommonModule, Avatar],
  templateUrl: './avatar-test.html',
  styleUrl: './avatar-test.scss'
})
export class AvatarTest {
  // Sample avatar data for testing
  sampleAvatars: AvatarData[] = [
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-blue-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-green-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-purple-500'
    },
    {
      name: '<PERSON>',
      initials: 'S<PERSON>',
      backgroundColor: 'bg-pink-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON>',
      backgroundColor: 'bg-yellow-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-red-500'
    }
  ];

  largeAvatarGroup: AvatarD<PERSON>[] = [
    { name: '<PERSON>', initials: '<PERSON>' },
    { name: '<PERSON>', initials: '<PERSON><PERSON>' },
    { name: '<PERSON>', initials: '<PERSON>' },
    { name: '<PERSON> <PERSON>', initials: 'DP' },
    { name: '<PERSON> <PERSON>', initials: 'E<PERSON>' },
    { name: '<PERSON> <PERSON>', initials: 'FA' },
    { name: '<PERSON> <PERSON>', initials: 'G<PERSON>' },
    { name: '<PERSON>', initials: '<PERSON><PERSON>' }
  ];
}
