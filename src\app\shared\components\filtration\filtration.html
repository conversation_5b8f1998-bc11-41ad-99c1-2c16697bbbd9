
<div class="relative">
    <button class="button button--lg button-neutral me-5" (click)="toggleFilter()">
        <sdga-svg-icon name="filter" svgClass="me-1 flex items-center"></sdga-svg-icon>
        Filter
        <sdga-svg-icon name="arrow-down" svgClass="ms-1 flex items-center"></sdga-svg-icon>
    </button>
    <div class="flex items-center gap-4 mt-4" *ngIf="showFilterItems">
        <span class="chip chip-primary selected">
            Item
            <button class="flex items-center ms-1">
                <sdga-svg-icon name="close-menu"></sdga-svg-icon>
            </button>
        </span>
        <span class="chip chip-primary selected">
            Item
            <button class="flex items-center ms-1">
                <sdga-svg-icon name="close-menu"></sdga-svg-icon>
            </button>
        </span>
        <span class="chip chip-primary selected">
            Item
            <button class="flex items-center ms-1">
                <sdga-svg-icon name="close-menu"></sdga-svg-icon>
            </button>
        </span>
    </div>
    <div class="filter-content" *ngIf="isFilterOpened">
        <!-- <p (click)="showFilterItems = !showFilterItems">Filter Content</p> -->
      <form [formGroup]="testInputs" (ngSubmit)="onSubmit()" class="space-y-6">
        <div class="space-y-3">
          <h3 class="text-sm font-medium text-gray-700">Filter Options</h3>

          <sdga-form-input
            name="radioGroup"
            type="radio"
            label="Category A"
            [control]="testInputs.get('radioGroup')!"
            [formSubmitted]="formSubmitted"
            radioValue="categoryA"
            radioSize="md"
            (valueChanged)="onRadioToggle($event)"
          ></sdga-form-input>

          <sdga-form-input
            name="radioGroup"
            type="radio"
            label="Category B"
            [control]="testInputs.get('radioGroup')!"
            [formSubmitted]="formSubmitted"
            radioValue="categoryB"
            radioSize="md"
            (valueChanged)="onRadioToggle($event)"
          ></sdga-form-input>

          <sdga-form-input
            name="radioGroup"
            type="radio"
            label="Category C"
            [control]="testInputs.get('radioGroup')!"
            [formSubmitted]="formSubmitted"
            radioValue="categoryC"
            radioSize="md"
            (valueChanged)="onRadioToggle($event)"
          ></sdga-form-input>
        </div>
      </form>
    </div>
</div>