import { Component } from '@angular/core';
import { Menu } from "../../shared/components/menu/menu";
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'sdga-menu-test',
  imports: [Menu],
  templateUrl: './menu-test.html',
  styleUrl: './menu-test.scss'
})
export class MenuTest {

    testInputs?: FormGroup;
    formSubmitted = false;
  menuGroups = [
    {
      label: 'Group Label',
      items: [
        { icon: 'file-add', label: 'Item 1', checked: true, switch: false, badge: null },
        { icon: 'file-add', label: 'Item 2', checked: false, switch: false, badge: null, count: '+99' },
        { icon: 'file-add', label: 'Item 3', checked: false, switch: false, badge: null , arrow: true }
      ]
    },
    {
      label: 'Group Label',
      items: [
        { icon: 'file-add', label: 'Item 4', switch: true, checked: false, badge: null, selected: true }
      ]
    },
    {
      label: 'Group Label',
      items: [
        { icon: 'file-add', label: 'Item 5', badge: 'Label', badgeClass: 'label label-md label-success', checked: false, switch: false }
      ]
    },
  ];
}
