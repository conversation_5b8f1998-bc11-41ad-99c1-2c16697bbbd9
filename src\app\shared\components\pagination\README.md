# SDGA Pagination Component

A reusable pagination component following the Saudi Digital Government Design System specifications.

## Features

- ✅ **SDGA Design System Compliant**: Follows official design specifications
- ✅ **Tailwind CSS Only**: No custom CSS dependencies
- ✅ **RTL/LTR Support**: Full bidirectional text support
- ✅ **Interactive Ellipsis**: Dropdown menu for quick page navigation
- ✅ **Accessibility**: ARIA labels, keyboard navigation
- ✅ **TypeScript**: Full type safety with interfaces
- ✅ **Configurable**: Multiple size options and configurations
- ✅ **Responsive**: Works on all screen sizes

## Basic Usage

```typescript
import { Pagination, PageChangeEvent } from '@shared/components/pagination/pagination';

@Component({
  imports: [Pagination],
  template: `
    <sdga-pagination
      [totalItems]="totalItems"
      [itemsPerPage]="itemsPerPage"
      [currentPage]="currentPage"
      [maxVisiblePages]="3"
      (pageChange)="onPageChange($event)">
    </sdga-pagination>
  `
})
export class MyComponent {
  totalItems = 100;
  itemsPerPage = 10;
  currentPage = 1;

  onPageChange(event: PageChangeEvent): void {
    this.currentPage = event.page;
    // Load new data
  }
}
```

## Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `totalItems` | `number` | `0` | Total number of items to paginate |
| `itemsPerPage` | `number` | `10` | Number of items per page |
| `currentPage` | `number` | `1` | Current active page |
| `maxVisiblePages` | `number` | `3` | Maximum number of page buttons to show |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Size of pagination buttons |
| `disabled` | `boolean` | `false` | Disable all pagination controls |
| `showFirstLast` | `boolean` | `true` | Show first/last page buttons |
| `showPrevNext` | `boolean` | `true` | Show previous/next buttons |
| `ariaLabel` | `string` | `'Pagination Navigation'` | ARIA label for navigation |

## Events

| Event | Type | Description |
|-------|------|-------------|
| `pageChange` | `PageChangeEvent` | Emitted when page changes |

### PageChangeEvent Interface

```typescript
interface PageChangeEvent {
  page: number;           // New current page
  itemsPerPage: number;   // Items per page
  totalItems: number;     // Total items
  totalPages: number;     // Total pages
}
```

## Size Variations

```html
<!-- Small -->
<sdga-pagination size="small" [totalItems]="100" [itemsPerPage]="10"></sdga-pagination>

<!-- Medium (Default) -->
<sdga-pagination size="medium" [totalItems]="100" [itemsPerPage]="10"></sdga-pagination>

<!-- Large -->
<sdga-pagination size="large" [totalItems]="100" [itemsPerPage]="10"></sdga-pagination>
```

## Advanced Configuration

```html
<sdga-pagination
  [totalItems]="1000"
  [itemsPerPage]="25"
  [currentPage]="5"
  [maxVisiblePages]="7"
  [disabled]="isLoading"
  size="large"
  ariaLabel="Search Results Pagination"
  (pageChange)="handlePageChange($event)">
</sdga-pagination>
```

## Accessibility Features

- ARIA labels for screen readers
- Keyboard navigation support
- Focus management
- Current page indication
- Disabled state handling

## RTL Support

The component automatically supports RTL layouts:
- Arrow icons rotate appropriately
- Spacing and alignment adjust for RTL
- Uses `rtl:` and `ltr:` Tailwind prefixes

## Styling

All styling is handled through Tailwind CSS utility classes. The component uses:
- SDGA color palette (`sa-500` for active page underline)
- Clean, minimal design with no borders on page numbers
- Active page indicated by green underline (not background)
- Interactive ellipsis with dropdown for page selection
- SVG icons for navigation arrows (using sdga-svg-icon component)
- No focus outlines for cleaner appearance
- Hover states for better user experience
- Responsive design patterns

## Interactive Ellipsis

The ellipsis (`...`) is now clickable and opens a dropdown menu:
- **Click ellipsis** to see available page numbers
- **Select any page** from the dropdown to navigate directly
- **Visual indicator** shows current active page with checkmark icon
- **Highlighted active page** with SDGA green background color
- **Auto-closes** when clicking outside or selecting a page
- **RTL support** with proper dropdown positioning and icon placement

## Examples

See comprehensive examples in `src/app/examples/tailwind-classes-test/tailwind-classes-test.html`
