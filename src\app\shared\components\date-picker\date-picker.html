<div
  *ngIf="isOpen"
  class="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-sa-2xl z-50 p-6 min-w-[320px]"
  (click)="onCalendarClick($event)"
>
  <!-- Calendar Header -->
  <div class="flex items-center justify-between mb-4">
    <button 
      type="button"
      (click)="previousMonth()"
      class="p-1 hover:bg-gray-100 rounded transition-colors duration-200"
    >
      <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    
    <h3 class="text-sm font-medium text-gray-900">
      {{ getCurrentMonthYear() }}
    </h3>
    
    <button 
      type="button"
      (click)="nextMonth()"
      class="p-1 hover:bg-gray-100 rounded transition-colors duration-200"
    >
      <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>
  </div>

  <!-- Day Names Header -->
  <div class="grid grid-cols-7 gap-1 mb-2">
    <div 
      *ngFor="let dayName of dayNames" 
      class="text-xs font-medium text-gray-500 text-center py-2"
    >
      {{ dayName }}
    </div>
  </div>

  <!-- Calendar Grid -->
  <div class="grid grid-cols-7 gap-1">
    <ng-container *ngFor="let week of weeks">
      <button
        *ngFor="let date of week"
        type="button"
        (click)="selectDate(date)"
        class="relative h-8 w-8 text-sm rounded-full transition-all duration-200 flex items-center justify-center"
[ngClass]="{
          'text-gray-900': isCurrentMonth(date),
          'text-gray-400': !isCurrentMonth(date),
          'border-2 border-sa-500': isToday(date) && !isDateSelected(date),
          'bg-sa-600 text-white font-medium': (isRangeEndpoint(date) && isRange) || (!isRange && isDateSelected(date)),
          'bg-sa-100': isDateInRange(date) && isRange,
          'hover:bg-gray-100': !isDateSelected(date) && !isDateDisabled(date) && !isToday(date),
          'hover:bg-sa-50': isToday(date) && !isDateSelected(date) && !isDateDisabled(date),
          'opacity-50 cursor-not-allowed': isDateDisabled(date),
          'cursor-pointer': !isDateDisabled(date)
        }"
        [disabled]="isDateDisabled(date)"
      >
        {{ date.getDate() }}
      </button>
    </ng-container>
  </div>

  <!-- Range Selection Info -->
  <div *ngIf="isRange && isSelectingRange && rangeStartDate" class="mt-3 pt-3 border-t border-gray-200">
    <p class="text-xs text-gray-600 text-center">
      Select end date for range starting {{ rangeStartDate | date:'MMM d, y' }}
    </p>
  </div>
</div>
