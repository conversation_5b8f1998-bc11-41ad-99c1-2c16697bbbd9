import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';

@Component({
  selector: 'sdga-filtration',
  imports: [CommonModule, SvgIcon],
  templateUrl: './filtration.html',
  styleUrl: './filtration.scss'
})
export class Filtration {
  isFilterOpened: boolean = false;

toggleFilter() {
  this.isFilterOpened = !this.isFilterOpened
} 

}
