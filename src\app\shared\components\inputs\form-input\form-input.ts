import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { DatePicker, DateRange } from '../../date-picker/date-picker';

@Component({
  selector: 'sdga-form-input',
  imports: [CommonModule, DatePicker],
  templateUrl: './form-input.html',
  styleUrl: './form-input.scss'
})
export class FormInput {
 @Input() label: string | undefined;
  @Input() name: string | undefined;
  @Input() min: string = "";
  @Input() max: string = "";
  @Input() type: string = 'text';
  @Input() step: string = '1';
  @Input() required: boolean = false;
  @Input() formSubmitted: boolean = false;
  @Input() disabled: boolean = false;
  @Input() isReadOnly:boolean = false;
  @Input() isReversedCheckBox: boolean = false; // ✅ New property
  @Input() control: AbstractControl | undefined;
  @Input() value:  string | boolean | null = null;
  @Input() unClickable: boolean = false;
  @Input()class!: string ;
  @Input() readonly: boolean = false;
  @Input() checkboxSize: 'sm' | 'md' | 'lg' = 'sm'; // ✅ Checkbox size property
  @Input() description: string | undefined; // ✅ Description text for checkbox
  @Input() isDateRange: boolean = false; // ✅ Date range picker flag
  @Input() minDate: Date | null = null; // ✅ Minimum selectable date
  @Input() maxDate: Date | null = null; // ✅ Maximum selectable date
  @Output() toggleChange = new EventEmitter<any>();
  @Output() valueChanged = new EventEmitter<any>();
  @Output() dateSelected = new EventEmitter<Date>();
  @Output() rangeSelected = new EventEmitter<DateRange>();

  translatedLabel: string | undefined;
  isFocused: boolean = false;
  isDatePickerOpen: boolean = false;
  selectedDate: Date | null = null;
  selectedRange: DateRange | null = null;
  onChange = (value: any) => {};
  onTouched = () => {};

  // constructor(private translate: TranslateService) {}

  ngOnInit(): void {
    this.translatedLabel = this.label;
    // this.translatedLabel = this.translate.instant(this.label);
  }

  writeValue(value: any): void {
    if (this.isReversedCheckBox && this.type == 'checkbox' && value == null) {
      value = false;
      if (this.control instanceof FormControl) {
        this.control.setValue(value);
      }
      // this.control?.setValue(value);
      value = !value;
    } else if (this.isReversedCheckBox) {
      value = !value;
    }
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
  onBlur() {
    // Don't set isFocused to false for date inputs when date picker is open
    if (this.type === 'date' || this.isDateRange) {
      if (!this.isDatePickerOpen) {
        this.isFocused = false;
      }
    } else {
      this.isFocused = false;
    }
    this.valueChanged.emit(this.value);
  }

  onFocus() {
    this.isFocused = true;
    if (this.type === 'date' || this.isDateRange) {
      this.openDatePicker();
    }
  }

  openDatePicker() {
    if (!this.isDatePickerOpen) {
      // Add a small delay to prevent immediate closing from document click
      setTimeout(() => {
        this.isDatePickerOpen = true;
        this.isFocused = true;
      }, 50);
    }
  }

  closeDatePicker() {
    this.isDatePickerOpen = false;
    this.isFocused = false;
  }

  onDateSelected(date: Date) {
    this.selectedDate = date;
    this.value = this.formatDate(date);
    if (this.control instanceof FormControl) {
      this.control.setValue(this.value);
    }
    this.onChange(this.value);
    this.dateSelected.emit(date);
    this.valueChanged.emit(this.value);
  }

  onRangeSelected(range: DateRange) {
    this.selectedRange = range;
    this.value = this.formatDateRange(range);
    if (this.control instanceof FormControl) {
      this.control.setValue(this.value);
    }
    this.onChange(this.value);
    this.rangeSelected.emit(range);
    this.valueChanged.emit(this.value);
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDateRange(range: DateRange): string {
    if (!range.startDate || !range.endDate) {
      return '';
    }
    return `${this.formatDate(range.startDate)} - ${this.formatDate(range.endDate)}`;
  }

  getDisplayValue(): string {
    if (this.type === 'date' && this.selectedDate) {
      return this.formatDate(this.selectedDate);
    }
    if (this.isDateRange && this.selectedRange) {
      return this.formatDateRange(this.selectedRange);
    }
    return this.value as string || '';
  }

  /** ✅ Handles input change and propagates value */
  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement) {
      let value: any;

      if (this.type === 'checkbox') {
        value = inputElement.checked;

        // ✅ Apply isDisabled logic
        if (this.isReversedCheckBox) {
          value = !value;
        }
      } else {
        value = inputElement.value.trim();
      }

      this.value = value;
      if (this.control instanceof FormControl) {
        this.control.setValue(value);
      }
      this.onChange(value);
      this.toggleChange.emit(value);
    }
  }
  preventIfDate(event: any): void {
    if (this.type === 'date') {
      event.preventDefault();
    }
  }
}


