.filter-dropdown {
    @apply
    absolute
    top-full
    left-0
    mt-4
    p-4
    bg-white
    border
    border-gray-300
    rounded-lg
    shadow-lg
    min-w-64
    z-50
    opacity-0
    transform
    scale-95
    transition-all
    duration-200
    ease-out;

    // Animation for dropdown appearance
    animation: dropdownFadeIn 0.2s ease-out forwards;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Optional: Style for active button state
.button.active {
    @apply bg-gray-100 border-gray-400;
}