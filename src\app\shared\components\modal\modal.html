<div [ngClass]="containerClasses" *ngIf="isVertical">
  <div class="flex justify-between">
    <sdga-svg-icon [name]="'modalIcon'" [svgClass]="iconClass"></sdga-svg-icon>
    <sdga-svg-icon
      [name]="ButtonClose"
      [svgClass]="closeIconClass"
    ></sdga-svg-icon>
  </div>
  <div [ngClass]="contentClasses">
    <div [ngClass]="contentTitleClasse">{{ title }}</div>
    <div [ngClass]="TextContent">{{ content }}</div>
    <div class="mt-2 flex gap-2 justify-between">
      <div>
        <button
          class="py-3 px-3 font-medium text-default-100 border border-gray-300 rounded-sm"
        >
          <sdga-svg-icon
            [name]="'blackActionIcon'"
            [svgClass]="'inline-block'"
          ></sdga-svg-icon>
          <span class="px-1 text-sm font-medium">button</span>
        </button>
      </div>
      <div>
        <button
          class="py-3 px-3 font-medium text-default-100 border border-gray-300 rounded-sm"
        >
          <sdga-svg-icon
            [name]="'blackActionIcon'"
            [svgClass]="'inline-block'"
          ></sdga-svg-icon>
          <span class="px-1 text-sm font-medium">button</span>
        </button>

        <button
          class="bg-[color:theme('colors.default.100')] text-white py-3 px-3 font-medium border border-gray-300 rounded-sm mx-2"
        >
          <sdga-svg-icon
            [name]="'blackActionIcon'"
            [svgClass]="'inline-block'"
          ></sdga-svg-icon>
          <span class="px-1 text-sm font-medium">button</span>
        </button>
      </div>
    </div>
  </div>
</div>
<div [ngClass]="containerClasses" *ngIf="!isVertical">
  <div class="flex justify-between">
    <div class="icon p-2">
      <sdga-svg-icon
        [name]="'modalIcon'"
        [svgClass]="iconClass"
      ></sdga-svg-icon>
    </div>
    <div class="close flex items-center">
      <sdga-svg-icon
        [name]="ButtonClose"
        [svgClass]="closeIconClass"
      ></sdga-svg-icon>
    </div>
  </div>
  <div class="content">
    <div [ngClass]="contentTitleClasse">{{title}}</div>
    <div [ngClass]="TextContent">{{content}}</div>

    <div class="actions flex flex-col">
      <button
        class="bg-[color:theme('colors.default.100')] text-white py-3 px-3 font-medium border border-gray-300 rounded-sm  my-1"
      >
        <sdga-svg-icon
          [name]="'blackActionIcon'"
          [svgClass]="'inline-block'"
        ></sdga-svg-icon>
        <span class="px-1 text-sm font-medium">button</span>
      </button>
      <button
        class="py-3 px-3 font-medium text-default-100 border border-gray-300 rounded-sm my-1"
      >
        <sdga-svg-icon
          [name]="'blackActionIcon'"
          [svgClass]="'inline-block'"
        ></sdga-svg-icon>
        <span class="px-1 text-sm font-medium">button</span>
      </button>

      <button
        class="py-3 px-3 font-medium text-default-100  rounded-sm my-1"
      >
        <sdga-svg-icon
          [name]="'blackActionIcon'"
          [svgClass]="'inline-block'"
        ></sdga-svg-icon>
        <span class="px-1 text-sm font-medium">button</span>
      </button>
    </div>
  </div>
</div>
