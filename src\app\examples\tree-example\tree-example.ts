import { CommonModule } from '@angular/common';
import { Component, AfterViewInit, TemplateRef, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Tree, TreeNode } from '../../shared/components/tree/tree';

@Component({
  selector: 'sdga-tree-example',
  standalone: true,
  imports: [CommonModule, TranslateModule, Tree],
  templateUrl: './tree-example.html',
  styleUrl: './tree-example.scss'
})
export class TreeExample implements AfterViewInit {

  // Template references for content sharing
  @ViewChild('loginFormTpl') loginFormTpl!: TemplateRef<any>;
  @ViewChild('list1Tpl') list1Tpl!: TemplateRef<any>;
  @ViewChild('list2Tpl') list2Tpl!: TemplateRef<any>;
  @ViewChild('text1Tpl') text1Tpl!: TemplateRef<any>;
  @ViewChild('text2Tpl') text2Tpl!: TemplateRef<any>;

  // Tree structure with content under each title
  treeData: TreeNode[] = [
    {
      id: 'tree-title',
      label: 'Tree Title',
      labelKey: 'TREE_EXAMPLE.TREE_TITLE',
      expanded: true, // Start expanded to show border
      children: [
        {
          id: 'login-form',
          label: 'Form for Login',
          labelKey: 'TREE_EXAMPLE.LOGIN_FORM',
          expanded: false, // Can be collapsed
          children: [
            {
              id: 'login-form-content',
              label: 'Login Form Content',
              labelKey: 'TREE_EXAMPLE.LOGIN_FORM_CONTENT'
              // content will be set in ngAfterViewInit
            }
          ]
        },
        {
          id: 'lists',
          label: 'Lists',
          labelKey: 'TREE_EXAMPLE.LISTS',
          expanded: false, // Can be collapsed
          children: [
            {
              id: 'list-1',
              label: 'List 1',
              labelKey: 'TREE_EXAMPLE.LIST_1',
              expanded: false, // Can be collapsed
              children: [
                {
                  id: 'list-1-content',
                  label: 'List 1 Content',
                  labelKey: 'TREE_EXAMPLE.LIST_1_CONTENT',
                  // content will be set in ngAfterViewInit
                }
              ]
            },
            {
              id: 'list-2',
              label: 'List 2',
              labelKey: 'TREE_EXAMPLE.LIST_2',
              expanded: false, // Can be collapsed
              children: [
                {
                  id: 'list-2-content',
                  label: 'List 2 Content',
                  labelKey: 'TREE_EXAMPLE.LIST_2_CONTENT'
                  // content will be set in ngAfterViewInit
                }
              ]
            }
          ]
        },
        {
          id: 'free-text',
          label: 'Free Text',
          labelKey: 'TREE_EXAMPLE.FREE_TEXT',
          expanded: false, // Can be collapsed
          children: [
            {
              id: 'text-1',
              label: 'Text 1',
              labelKey: 'TREE_EXAMPLE.TEXT_1',
              expanded: false, // Can be collapsed
              children: [
                {
                  id: 'text-1-content',
                  label: 'Text 1 Content',
                  labelKey: 'TREE_EXAMPLE.TEXT_1_CONTENT'
                  // content will be set in ngAfterViewInit
                }
              ]
            },
            {
              id: 'text-2',
              label: 'Text 2',
              labelKey: 'TREE_EXAMPLE.TEXT_2',
              expanded: false, // Can be collapsed
              children: [
                {
                  id: 'text-2-content',
                  label: 'Text 2 Content',
                  labelKey: 'TREE_EXAMPLE.TEXT_2_CONTENT'
                  // content will be set in ngAfterViewInit
                }
              ]
            }
          ]
        }
      ]
    }
  ];

  constructor(private translateService: TranslateService) {}

  ngAfterViewInit(): void {
    // Set template references to content nodes after ViewChild is available
    const treeTitle = this.treeData[0];
    if (treeTitle.children) {
      // Login Form Content
      const loginForm = treeTitle.children[0];
      if (loginForm.children) {
        loginForm.children[0].content = this.loginFormTpl;
        loginForm.children[0].expanded = true; // Content nodes are always expanded
      }

      // Lists section
      const listsSection = treeTitle.children[1];
      if (listsSection.children) {
        // List 1 Content
        const list1 = listsSection.children[0];
        if (list1.children) {
          list1.children[0].content = this.list1Tpl;
          list1.children[0].expanded = true; // Content nodes are always expanded
        }

        // List 2 Content
        const list2 = listsSection.children[1];
        if (list2.children) {
          list2.children[0].content = this.list2Tpl;
          list2.children[0].expanded = true; // Content nodes are always expanded
        }
      }

      // Free Text section
      const freeTextSection = treeTitle.children[2];
      if (freeTextSection.children) {
        // Text 1 Content
        const text1 = freeTextSection.children[0];
        if (text1.children) {
          text1.children[0].content = this.text1Tpl;
          text1.children[0].expanded = true; // Content nodes are always expanded
        }

        // Text 2 Content
        const text2 = freeTextSection.children[1];
        if (text2.children) {
          text2.children[0].content = this.text2Tpl;
          text2.children[0].expanded = true; // Content nodes are always expanded
        }
      }
    }
  }

  onNodeClick(node: TreeNode): void {
    console.log('Node clicked:', node.label);
  }

  switchLanguage(lang: string): void {
    this.translateService.use(lang);
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;
  }

  getCurrentLanguage(): string {
    return this.translateService.currentLang || 'en';
  }
}
