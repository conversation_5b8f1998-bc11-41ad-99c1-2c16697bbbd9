import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, Input } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'sdga-svg-icon',
  imports: [CommonModule],
  templateUrl: './svg-icon.html',
  styleUrl: './svg-icon.scss'
})
export class SvgIcon {
@Input() name!: string;
  svgContent: SafeHtml = '';
@Input() svgClass = '';
  constructor(private http: HttpClient, private sanitizer: DomSanitizer) {}

ngOnInit(): void {
  const url = `assets/icons/${this.name}.svg`;
  this.http.get(url, { responseType: 'text' }).subscribe((data) => {
    const fixed = data.replace(/fill="#[0-9a-fA-F]{3,6}"/g, 'fill="currentColor"');
    this.svgContent = this.sanitizer.bypassSecurityTrustHtml(fixed);
  });
}
}
