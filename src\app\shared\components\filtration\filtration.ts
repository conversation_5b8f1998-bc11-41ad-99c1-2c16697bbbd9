import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormInput } from '../inputs/form-input/form-input';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'sdga-filtration',
  imports: [CommonModule, SvgIcon, FormInput, TranslateModule, ReactiveFormsModule ],
  templateUrl: './filtration.html',
  styleUrl: './filtration.scss'
})
export class Filtration {
  testInputs: FormGroup;
  isFilterOpened: boolean = false;
  showFilterItems: boolean = false;
  formSubmitted: boolean = false;

  constructor(private fb: FormBuilder) {
    this.testInputs = this.fb.group({
      text: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      number: ['', [Validators.required]],
      decimal: ['', [Validators.required]],
      textarea: ['', [Validators.required, Validators.maxLength(100)]],
      checkboxSm: [false],
      checkboxMd: [true],
      checkboxLg: [false, [Validators.requiredTrue]],
      radioGroup: [''], // Added missing radio group control
    })
  }

  toggleFilter() {
    this.isFilterOpened = !this.isFilterOpened
  }

  onSubmit() {
    throw new Error('Method not implemented.');
  }

  onRadioToggle(value: any) {
    const currentValue = this.testInputs.get('radioGroup')?.value;

    // If the same radio button is clicked again, uncheck it
    if (currentValue === value) {
      this.testInputs.get('radioGroup')?.setValue('');
    } else {
      // Otherwise, select the new value
      this.testInputs.get('radioGroup')?.setValue(value);
    }
  }

}
