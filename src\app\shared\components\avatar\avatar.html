<!-- Single Avatar -->
<div *ngIf="!isGroup"
     [attr.aria-label]="alt || name || 'Avatar'">
     <!-- Image Avatar -->
     <img *ngIf="src"
          [src]="src"
          [alt]="alt || name || 'Avatar'"
          class="object-cover w-full h-full"
          (error)="onImageError()">
     
     <!-- Icon Avatar -->
     <div *ngIf="icon"
          class="flex items-center justify-center text-gray-600 bg-gray-200"
          [ngClass]="[shapeClasses[shape || 'circle'], sizeClasses[size || 'sm']]">
       <sdga-svg-icon [name]="icon!" [svgClass]="'w-1/2 h-1/2'"></sdga-svg-icon>
     </div>
     
     <!-- Initials Avatar -->
     <div *ngIf="false"
          class="flex items-center justify-center text-white bg-gray-200"
          [ngClass]="[shapeClasses[shape || 'circle'], sizeClasses[size || 'sm']]">
       {{ getDisplayInitials() }}
     </div>
</div>

<!-- Avatar Group -->
<div *ngIf="isGroup"
     class="flex items-center"
     [attr.aria-label]="'Avatar group with ' + avatars?.length + ' members'">

  <!-- Individual Avatars in Group -->
  <div *ngFor="let avatar of displayedAvatars; let i = index; trackBy: trackByIndex"
       class="relative inline-flex items-center justify-center overflow-hidden border-2 border-white shadow-sm"
       [ngClass]="[sizeClasses[size || 'sm'], shapeClasses[shape || 'circle']]"
       [style.margin-left]="i > 0 ? '-0.5rem' : '0'"
       [style.z-index]="displayedAvatars.length - i"
       [attr.aria-label]="avatar.alt || avatar.name || 'Avatar ' + (i + 1)">

    <!-- Group Avatar Image -->
    <img *ngIf="avatar.src"
         [src]="avatar.src"
         [alt]="avatar.alt || avatar.name || 'Avatar'"
         class="object-cover w-full h-full"
         (error)="onGroupImageError(i)">

    <!-- Group Avatar Icon -->
    <div *ngIf="avatar.icon && !avatar.src"
         class="flex items-center justify-center w-full h-full text-gray-600 bg-gray-200">
      <sdga-svg-icon [name]="avatar.icon"
                     [svgClass]="'w-1/2 h-1/2'"></sdga-svg-icon>
    </div>

    <!-- Group Avatar Initials -->
    <div *ngIf="!avatar.src && !avatar.icon"
         class="flex items-center justify-center w-full h-full font-medium text-white"
         [ngClass]="getGroupAvatarBackground(avatar, i)">
      {{ getGroupAvatarInitials(avatar) }}
    </div>
  </div>

  <!-- Remaining Count Indicator -->
  <div *ngIf="remainingCount > 0"
       class="inline-flex items-center justify-center overflow-hidden font-medium text-gray-600 bg-gray-100 border-2 border-white shadow-sm"
       [ngClass]="[sizeClasses[size || 'sm'], shapeClasses[shape || 'circle']]"
       [style.margin-left]="'-0.5rem'"
       [style.z-index]="0"
       [attr.aria-label]="remainingCount + ' more members'">
    +{{ remainingCount }}
  </div>
</div>