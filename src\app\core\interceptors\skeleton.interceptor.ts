// loading.interceptor.ts
import { inject, Injectable } from '@angular/core';
import {
  <PERSON>ttpEvent, HttpHandler, HttpInterceptor, HttpInterceptorFn, HttpRequest
} from '@angular/common/http';
import { finalize } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { SkeletonloadingService } from '../services/skeletonloading-service';

export const LoadingInterceptor: HttpInterceptorFn = (req, next) => {
  const loadingService = inject(SkeletonloadingService);
  loadingService.show();

  return next(req).pipe(
    finalize(() => loadingService.hide())
  );
};
