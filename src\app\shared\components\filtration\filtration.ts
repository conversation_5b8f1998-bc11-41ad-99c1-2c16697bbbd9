import { CommonModule } from '@angular/common';
import { Component, ElementRef, HostListener } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';

@Component({
  selector: 'sdga-filtration',
  imports: [CommonModule, SvgIcon],
  templateUrl: './filtration.html',
  styleUrl: './filtration.scss'
})
export class Filtration {
  isFilterOpened: boolean = false;

  constructor(private elementRef: ElementRef) {}

  toggleFilter() {
    this.isFilterOpened = !this.isFilterOpened;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    // Close dropdown if clicking outside the component
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.isFilterOpened = false;
    }
  }
}
