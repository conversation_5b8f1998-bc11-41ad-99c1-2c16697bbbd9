import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'sdga-content-switcher',
  imports: [CommonModule],
  templateUrl: './content-switcher.html',
  styleUrl: './content-switcher.scss'
})
export class ContentSwitcher {
  @Input() items: string[] | undefined;
  @Input() selectedIndex = 0;
  @Input() size: 'sm' | 'md' | 'lg' | undefined;
  @Input() hasBackground: boolean | undefined = false;

  @Output() switched = new EventEmitter<number>();

  select(i: number) {
    this.selectedIndex = i;
    this.switched.emit(i);
  }

  sizeClasses = {
    sm: 'px-2 py-1 text-md',
    md: 'px-3 py-sm text-lg',
    lg: 'px-4 py-md text-xl',
  };

  activeClasses = 'bg-gray-950 text-white border-e last:border-e-0 border-gray-950';
  inactiveClasses = 'bg-gray-100 text-default-100 border-e last:border-e-0 border-gray-300';

  hasbgActiveClasses = 'bg-sa-600 text-white border-e last:border-e-0 border-sa-600';
  hasbgInactiveClasses = 'bg-white bg-opacity-20 text-white border-e last:border-e-0 border-white border-opacity-10';
}
