import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';

@Component({
  selector: 'sdga-rating',
  imports: [CommonModule , SvgIcon],
  templateUrl: './rating.html',
  styleUrl: './rating.scss'
})
export class Rating {

  @Input() rating = 0;
  @Input() max = 5;
  @Input() readonly = false;
  @Input() starColor : string = '';

  @Output() ratingChange = new EventEmitter<number>();

  stars: boolean[] = [];

  ngOnInit() {
    this.calculateStars();
  }

  ngOnChanges() {
    this.calculateStars();
  }

  calculateStars() {
    this.stars = Array(this.max).fill(false).map((_, i) => i < this.rating);
  }

    isStarFilled(index: number): boolean {
    return index < this.rating;
  }

  rate(index: number) {
    if (!this.readonly) {
      this.rating = index + 1;
      this.ratingChange.emit(this.rating);
      this.calculateStars();
    }
  }
}
