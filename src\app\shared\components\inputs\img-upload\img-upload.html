<div class="space-y-2">
  <!-- Label -->
  <label
    [attr.for]="name"
    class="block text-sm font-medium text-gray-700 transition-colors duration-200"
    [ngClass]="{
      'text-error-600': formSubmitted && control?.errors
    }"
  >
    {{ translatedLabel }}<span *ngIf="required" class="ml-1 rtl:ml-0 rtl:mr-1 text-error-500">*</span>
  </label>

  <!-- Upload Area -->
  <div class="relative">
    <!-- Single Upload Mode -->
    <div *ngIf="!multiple" class="relative">
      <!-- Upload Area - Always Visible -->
      <div
        class="relative p-6 text-left transition-all duration-300 ease-in-out cursor-pointer rtl:text-right hover:bg-gray-50"
        [ngClass]="{
          'bg-sa-25': isDragOver,
          'bg-error-25': formSubmitted && control?.errors,
          'animate-shake': errorMessage
        }"
        (click)="fileInput.click()"
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)"
      >
        <input
          type="file"
          #fileInput
          (change)="onFileSelected($event)"
          class="sr-only"
          [accept]="allowedTypes.join(',')"
          [required]="required"
          [attr.name]="name"
          [multiple]="multiple"
        />

        <!-- Upload Content -->
        <div class="space-y-4">
          <!-- Upload Title -->
          <h3 class="text-base font-medium text-left text-gray-900 rtl:text-right" translate="IMG_UPLOAD.UPLOAD_FILES">Upload files</h3>

          <!-- File Info -->
          <p class="text-sm text-left text-gray-500 rtl:text-right"
             [innerHTML]="'IMG_UPLOAD.FILE_INFO' | translate: {maxSize: getMaxSizeText(), allowedTypes: getAllowedTypesText()}">
          </p>

          <!-- Browse Files Button - Only show when no image selected -->
          <div class="text-left rtl:text-right">
            <button
              *ngIf="previewImages.length === 0"
              type="button"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white transition-colors duration-200 bg-gray-900 border border-transparent rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              (click)="fileInput.click(); $event.stopPropagation()"
              translate="IMG_UPLOAD.BROWSE_FILES"
            >
              Browse Files
            </button>
          </div>
        </div>

        <!-- Progress Bar -->
        <div *ngIf="isUploading" class="mt-4">
          <div class="w-full h-2 bg-gray-200 rounded-full">
            <div
              class="h-2 transition-all duration-300 rounded-full bg-sa-600"
              [style.width.%]="uploadProgress"
            ></div>
          </div>
          <p class="mt-1 text-xs text-left text-gray-500">
            {{ 'IMG_UPLOAD.UPLOADING' | translate: {progress: uploadProgress} }}
          </p>
        </div>
      </div>

      <!-- Single Image Preview with File Name -->
      <div *ngIf="previewImages.length > 0" class="mt-4">
        <!-- File Preview Area -->
        <div class="p-3 bg-gray-100 border border-gray-200 rounded-lg">
          <div class="flex items-center justify-between space-x-3 rtl:space-x-reverse">
            <div class="flex items-center space-x-3 rtl:space-x-reverse">
              <div class="flex-shrink-0">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-success-700">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-left text-gray-900 truncate rtl:text-right">
                  {{ previewImages[0].name }}
                </p>
              </div>
            </div>
            <button
              type="button"
              class="flex-shrink-0 w-6 h-6 text-gray-600 transition-colors duration-200 hover:text-gray-800 focus:outline-none"
              (click)="removeImage(0)"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Multiple Upload Mode -->
    <div *ngIf="multiple" class="space-y-4">
      <!-- Upload Area for Multiple -->
      <div
        class="relative p-8 text-center transition-all duration-300 ease-in-out bg-gray-100 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-gray-400 hover:bg-gray-50 sm:grid-cols-2"
        [ngClass]="{
          'border-sa-500 bg-sa-25': isDragOver,
          'border-error-500 bg-error-25': formSubmitted && control?.errors,
          'border-gray-300 bg-gray-100': !isDragOver && !(formSubmitted && control?.errors),
          'animate-shake': errorMessage
        }"
        (click)="fileInput.click()"
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)"
      >
        <input
          type="file"
          #fileInput
          (change)="onFileSelected($event)"
          class="sr-only"
          [accept]="allowedTypes.join(',')"
          [required]="required"
          [attr.name]="name"
          [multiple]="multiple"
        />

        <!-- Upload Content -->
        <div class="space-y-4">
          <!-- Upload Icon -->
          <div class="w-12 h-12 mx-auto text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
            </svg>
          </div>

          <!-- Upload Text -->
          <div class="space-y-2">
            <p class="text-base font-medium text-gray-900" translate="IMG_UPLOAD.DRAG_DROP_MULTIPLE">
              Drag and drop files here to upload
            </p>
            <p class="text-sm text-gray-500"
               [innerHTML]="'IMG_UPLOAD.FILE_INFO' | translate: {maxSize: getMaxSizeText(), allowedTypes: getAllowedTypesText()}">
            </p>
          </div>

          <!-- Browse Files Button -->
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white transition-colors duration-200 bg-gray-900 border border-transparent rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            (click)="fileInput.click(); $event.stopPropagation()"
            translate="IMG_UPLOAD.BROWSE_FILES"
          >
            Browse Files
          </button>
        </div>
      </div>

      <!-- Multiple Images Preview List -->
      <div *ngIf="previewImages.length > 0" class="space-y-2">
        <div *ngFor="let image of previewImages; let i = index" class="p-3 bg-gray-100 border border-gray-200 rounded-lg">
          <div class="flex items-center justify-between space-x-3 rtl:space-x-reverse">
            <div class="flex items-center space-x-3 rtl:space-x-reverse">
              <div class="flex-shrink-0">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-success-700">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-left text-gray-900 truncate rtl:text-right">
                  {{ image.name }}
                </p>
                <!-- Upload Progress for Individual Images -->
                <div *ngIf="image.isUploading" class="mt-1">
                  <div class="w-full h-1 bg-gray-200 rounded-full">
                    <div
                      class="h-1 transition-all duration-300 rounded-full bg-sa-600"
                      [style.width.%]="image.progress"
                    ></div>
                  </div>
                  <p class="mt-1 text-xs text-left text-gray-500 rtl:text-right">
                    {{ 'IMG_UPLOAD.UPLOADING' | translate: {progress: image.progress} }}
                  </p>
                </div>
              </div>
            </div>
            <button
              type="button"
              class="flex-shrink-0 w-6 h-6 text-gray-600 transition-colors duration-200 hover:text-gray-800 focus:outline-none"
              (click)="removeImage(i)"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-3 right-3 h-0.5 rounded-full transition-all duration-300 ease-in-out origin-center"
      [ngClass]="{
        'scale-x-100': isFocused,
        'scale-x-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <!-- Error Messages -->
  <div *ngIf="formSubmitted && control?.errors || errorMessage" class="mt-1 space-y-1">
    <div
      *ngIf="formSubmitted && control?.errors?.['required']"
      class="flex items-center space-x-1 text-xs text-error-600 rtl:space-x-reverse"
    >
      <svg class="flex-shrink-0 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span translate="FORM.IS_REQUIRED">This field is required</span>
    </div>

    <div
      *ngIf="errorMessage"
      class="flex items-center space-x-1 text-xs text-error-600 rtl:space-x-reverse"
    >
      <svg class="flex-shrink-0 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>{{ errorMessage }}</span>
    </div>
  </div>
</div>
