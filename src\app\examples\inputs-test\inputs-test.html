<div class="max-w-4xl mx-auto p-6 bg-gray-50 min-h-screen">
  <div class="bg-white rounded-lg shadow-sa-md p-6">
    <h1 class="text-display-md font-bold text-gray-900 mb-6">SDGA Form Input Component Test</h1>

    <form [formGroup]="testInputs" (ngSubmit)="onSubmit()" class="space-y-6">

      <!-- Text Inputs Section -->
      <div class="space-y-4">
        <h2 class="text-text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Text Inputs</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <sdga-form-input
            name="text"
            type="text"
            label="Required Text Input"
            [control]="testInputs.get('text')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            placeholder="Enter some text"
          ></sdga-form-input>

          <sdga-form-input
            name="email"
            type="email"
            label="Email Address"
            [control]="testInputs.get('email')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            placeholder="<EMAIL>"
          ></sdga-form-input>

          <sdga-form-input
            name="password"
            type="password"
            label="Password"
            [control]="testInputs.get('password')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            placeholder="Enter password"
          ></sdga-form-input>

          <sdga-form-input
            name="singleDate"
            type="date"
            label="Single Date Picker"
            [control]="testInputs.get('singleDate')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Number Inputs Section -->
      <div class="space-y-4">
        <h2 class="text-text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Number Inputs</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <sdga-form-input
            name="number"
            type="number"
            label="Integer Number"
            [control]="testInputs.get('number')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            placeholder="Enter a number"
            min="1"
            max="100"
          ></sdga-form-input>

          <sdga-form-input
            name="decimal"
            type="number"
            label="Decimal Number"
            [control]="testInputs.get('decimal')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            placeholder="Enter a decimal"
            step="any"
            min="0"
            max="999.99"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Date Picker Section -->
      <div class="space-y-4">
        <h2 class="text-text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Date Pickers</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <sdga-form-input
            name="dateRange"
            label="Date Range Picker"
            [control]="testInputs.get('dateRange')!"
            [formSubmitted]="formSubmitted"
            [isDateRange]="true"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Textarea Section -->
      <div class="space-y-4">
        <h2 class="text-text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Textarea</h2>

        <sdga-form-input
          name="textarea"
          type="textarea"
          label="Description (Max 100 characters)"
          [control]="testInputs.get('textarea')!"
          [formSubmitted]="formSubmitted"
          [required]="true"
          placeholder="Enter a description..."
        ></sdga-form-input>
      </div>

      <!-- Checkbox Section -->
      <div class="space-y-4">
        <h2 class="text-text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Enhanced Checkbox Design</h2>

        <div class="space-y-4">
          <sdga-form-input
            name="checkbox-sm"
            type="checkbox"
            label="Small Checkbox with Description"
            description="When a selection needs a further detailed explanation, it goes here."
            [control]="testInputs.get('checkboxSm')!"
            [formSubmitted]="formSubmitted"
            checkboxSize="sm"
          ></sdga-form-input>

          <sdga-form-input
            name="checkbox-md"
            type="checkbox"
            label="Medium Checkbox"
            description="This is a medium-sized checkbox with additional context information."
            [control]="testInputs.get('checkboxMd')!"
            [formSubmitted]="formSubmitted"
            checkboxSize="md"
          ></sdga-form-input>

          <sdga-form-input
            name="checkbox-lg"
            type="checkbox"
            label="Large Required Checkbox"
            description="This large checkbox demonstrates the error state when required but not selected."
            [control]="testInputs.get('checkboxLg')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            checkboxSize="lg"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Special States Section -->
      <div class="space-y-4">
        <h2 class="text-text-lg font-semibold text-gray-800 border-b border-gray-200 pb-2">Special States</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <sdga-form-input
            name="disabled"
            type="text"
            label="Disabled Input"
            [control]="testInputs.get('disabled')!"
            [formSubmitted]="formSubmitted"
            [disabled]="true"
          ></sdga-form-input>

          <sdga-form-input
            name="readonly"
            type="text"
            label="Readonly Input"
            [control]="testInputs.get('readonly')!"
            [formSubmitted]="formSubmitted"
            [readonly]="true"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-4 pt-6 border-t border-gray-200">
        <button
          type="submit"
          class="px-4 py-2 bg-sa-600 text-white rounded-md hover:bg-sa-700 focus:outline-none focus:ring-2 focus:ring-sa-500 focus:ring-offset-2 transition-colors duration-200"
        >
          Submit Form
        </button>

        <button
          type="button"
          (click)="resetForm()"
          class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
        >
          Reset Form
        </button>
      </div>

      <!-- Form Status -->
      <div class="mt-6 p-4 bg-gray-100 rounded-md">
        <h3 class="text-sm font-medium text-gray-700 mb-2">Form Status:</h3>
        <p class="text-xs text-gray-600">Valid: {{ testInputs.valid }}</p>
        <p class="text-xs text-gray-600">Submitted: {{ formSubmitted }}</p>
        <p class="text-xs text-gray-600">Value: {{ testInputs.value | json }}</p>
      </div>
    </form>
  </div>
</div>