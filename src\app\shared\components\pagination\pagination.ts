import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SvgIcon } from '../svg-icon/svg-icon';

export interface PaginationConfig {
  totalItems: number;
  itemsPerPage: number;
  currentPage: number;
  maxVisiblePages?: number;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

export interface PageChangeEvent {
  page: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
}

export interface PageItem {
  page: number;
  label: string;
  active: boolean;
  disabled: boolean;
  type: 'page' | 'prev' | 'next' | 'first' | 'last' | 'ellipsis';
  showDropdown?: boolean;
  dropdownPages?: number[];
}

@Component({
  selector: 'sdga-pagination',
  imports: [CommonModule, SvgIcon],
  templateUrl: './pagination.html',
  styleUrl: './pagination.scss'
})
export class Pagination implements OnInit, OnChanges {
  @Input() totalItems: number = 0;
  @Input() itemsPerPage: number = 10;
  @Input() currentPage: number = 1;
  @Input() maxVisiblePages: number = 3;
  @Input() showFirstLast: boolean = true;
  @Input() showPrevNext: boolean = true;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() disabled: boolean = false;
  @Input() ariaLabel: string = 'Pagination Navigation';

  @Output() pageChange = new EventEmitter<PageChangeEvent>();

  pages: PageItem[] = [];
  totalPages: number = 0;
  showDropdown: boolean = false;
  dropdownPages: number[] = [];

  ngOnInit(): void {
    this.calculatePages();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['totalItems'] || changes['itemsPerPage'] || changes['currentPage']) {
      this.calculatePages();
    }
  }

  private calculatePages(): void {
    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    this.pages = this.generatePageItems();
  }

  private generatePageItems(): PageItem[] {
    const pages: PageItem[] = [];

    // Previous button
    if (this.showPrevNext) {
      pages.push({
        page: this.currentPage - 1,
        label: 'Previous',
        active: false,
        disabled: this.currentPage <= 1 || this.disabled,
        type: 'prev'
      });
    }

    // Fixed pattern: Always show 1, 2, 3, ..., last (when > 4 pages)
    if (this.totalPages <= 4) {
      // Show all pages when 4 or fewer total pages
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push({
          page: i,
          label: i.toString(),
          active: this.currentPage === i,
          disabled: this.disabled,
          type: 'page'
        });
      }
    } else {
      // More than 4 pages: Always show 1, 2, 3, ..., last

      // Always show pages 1, 2, 3
      for (let i = 1; i <= 3; i++) {
        pages.push({
          page: i,
          label: i.toString(),
          active: this.currentPage === i,
          disabled: this.disabled,
          type: 'page'
        });
      }

      // Add ellipsis
      pages.push({
        page: -1,
        label: '...',
        active: false,
        disabled: true,
        type: 'ellipsis'
      });

      // Always show last page
      pages.push({
        page: this.totalPages,
        label: this.totalPages.toString(),
        active: this.currentPage === this.totalPages,
        disabled: this.disabled,
        type: 'page'
      });
    }

    // Next button
    if (this.showPrevNext) {
      pages.push({
        page: this.currentPage + 1,
        label: 'Next',
        active: false,
        disabled: this.currentPage >= this.totalPages || this.disabled,
        type: 'next'
      });
    }

    return pages;
  }



  onPageClick(pageItem: PageItem): void {
    if (pageItem.disabled || pageItem.active || pageItem.type === 'ellipsis') {
      return;
    }

    this.currentPage = pageItem.page;
    this.calculatePages();

    this.pageChange.emit({
      page: this.currentPage,
      itemsPerPage: this.itemsPerPage,
      totalItems: this.totalItems,
      totalPages: this.totalPages
    });
  }

  getSizeClasses(): string {
    switch (this.size) {
      case 'small':
        return 'text-sm';
      case 'large':
        return 'text-lg';
      default:
        return 'text-base';
    }
  }

  getButtonSizeClasses(): string {
    switch (this.size) {
      case 'small':
        return 'px-3 py-2 min-w-[32px] h-8 text-sm';
      case 'large':
        return 'px-4 py-3 min-w-[48px] h-12 text-lg';
      default:
        return 'px-3 py-2 min-w-[40px] h-10 text-base';
    }
  }

  trackByPage(_index: number, item: PageItem): string {
    return `${item.type}-${item.page}-${item.label}`;
  }

  toggleDropdown(pageItem: PageItem): void {
    // Close all other dropdowns first
    this.pages.forEach(item => {
      if (item !== pageItem) {
        item.showDropdown = false;
      }
    });

    // Toggle current dropdown
    pageItem.showDropdown = !pageItem.showDropdown;

    // Always regenerate dropdown pages when opening (to ensure current page is included)
    if (pageItem.showDropdown) {
      pageItem.dropdownPages = this.generateDropdownPages(pageItem);
    }
  }

  onDropdownPageClick(page: number): void {
    // Navigate to selected page first
    this.currentPage = page;

    // Recalculate pages with new current page
    this.calculatePages();

    // Emit page change event
    this.pageChange.emit({
      page: this.currentPage,
      itemsPerPage: this.itemsPerPage,
      totalItems: this.totalItems,
      totalPages: this.totalPages
    });

    // Close all dropdowns after a short delay to show the selection
    setTimeout(() => {
      this.pages.forEach(item => {
        item.showDropdown = false;
      });
    }, 150);
  }

  getDropdownPages(pageItem: PageItem): number[] {
    return pageItem.dropdownPages || [];
  }

  isRTL(): boolean {
    return document.documentElement.dir === 'rtl' ||
           document.documentElement.getAttribute('lang') === 'ar';
  }

  onNavClick(event: Event): void {
    // Prevent event bubbling to document
    event.stopPropagation();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(_event: Event): void {
    // Close all dropdowns when clicking outside
    this.closeAllDropdowns();
  }

  private closeAllDropdowns(): void {
    this.pages.forEach(item => {
      item.showDropdown = false;
    });
  }

  private generateDropdownPages(pageItem: PageItem): number[] {
    const pages: number[] = [];

    // Find the gap this ellipsis represents
    const pageIndex = this.pages.indexOf(pageItem);
    const prevPage = pageIndex > 0 ? this.pages[pageIndex - 1] : null;
    const nextPage = pageIndex < this.pages.length - 1 ? this.pages[pageIndex + 1] : null;

    let startPage = 1;
    let endPage = this.totalPages;

    // Determine the range based on surrounding pages
    if (prevPage && prevPage.type === 'page') {
      startPage = prevPage.page + 1;
    }

    if (nextPage && nextPage.type === 'page') {
      endPage = nextPage.page - 1;
    }

    // Generate page numbers for the dropdown
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }
}
