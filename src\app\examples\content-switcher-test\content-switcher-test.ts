import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ContentSwitcher } from '../../shared/components/content-switcher/content-switcher';

@Component({
  selector: 'sdga-content-switcher-test',
  imports: [CommonModule, ContentSwitcher],
  templateUrl: './content-switcher-test.html',
  styleUrl: './content-switcher-test.scss'
})
export class ContentSwitcherTest {
  selectedIndex1 = 0;
  selectedIndex2 = 0;

  onSwitchOne(e: any) {
    this.selectedIndex1 = e
  }

  onSwitchTwo(e: any) {
    this.selectedIndex2 = e
  }
}
