import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { DatePicker, DateRange } from '../../date-picker/date-picker';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { SvgIcon } from '../../svg-icon/svg-icon';

@Component({
  selector: 'sdga-form-input',
  imports: [CommonModule, DatePicker, TranslateModule, SvgIcon],
  templateUrl: './form-input.html',
  styleUrl: './form-input.scss'
})
export class FormInput {
 @Input() label: string | undefined;
  @Input() name: string | undefined;
  @Input() placeholder: string | undefined;
  @Input() min: string = "";
  @Input() max: string = "";
  @Input() type: string = 'text';
  @Input() step: string = '1';
  @Input() required: boolean = false;
  @Input() formSubmitted: boolean = false;
  @Input() disabled: boolean = false;
  @Input() isReadOnly:boolean = false;
  @Input() isReversedCheckBox: boolean = false; // ✅ New property
  @Input() control: AbstractControl | undefined;
  @Input() value:  string | boolean | null = null;
  @Input() unClickable: boolean = false;
  @Input()class!: string ;
  @Input() readonly: boolean = false;
  @Input() checkboxSize: 'sm' | 'md' | 'lg' = 'sm'; // ✅ Checkbox size property
  @Input() description: string | undefined; // ✅ Description text for checkbox
  @Input() isDateRange: boolean = false; // ✅ Date range picker flag
  @Input() minDate: Date | null = null; // ✅ Minimum selectable date
  @Input() maxDate: Date | null = null; // ✅ Maximum selectable date
  @Input() radioSize: 'sm' | 'md' | 'lg' = 'sm'; // ✅ Radio button size property
  @Input() radioValue: any; // ✅ Radio button value
  @Input() switchSize: 'sm' | 'md' | 'lg' = 'md'; // ✅ Switch size property
  @Input() enableVoiceSearch: boolean = false; // ✅ Voice search property
  @Input() showSearchIcon: boolean = false; // ✅ Show search icon property
  @Output() toggleChange = new EventEmitter<any>();
  @Output() valueChanged = new EventEmitter<any>();
  @Output() dateSelected = new EventEmitter<Date>();
  @Output() rangeSelected = new EventEmitter<DateRange>();
  @Output() voiceSearchStart = new EventEmitter<void>();
  @Output() voiceSearchEnd = new EventEmitter<string>();

  isFocused: boolean = false;
  isDatePickerOpen: boolean = false;
  selectedDate: Date | null = null;
  selectedRange: DateRange | null = null;
  onChange = (value: any) => {};
  onTouched = () => {};

  // Voice search properties
  isListening: boolean = false;
  recognition: any;

  ngOnInit(): void {
    // No need for translatedLabel since translation is handled in template
  }

  writeValue(value: any): void {
    if (this.isReversedCheckBox && this.type == 'checkbox' && value == null) {
      value = false;
      if (this.control instanceof FormControl) {
        this.control.setValue(value);
      }
      // this.control?.setValue(value);
      value = !value;
    } else if (this.isReversedCheckBox) {
      value = !value;
    }
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
  onBlur() {
    // Don't set isFocused to false for date inputs when date picker is open
    if (this.type === 'date' || this.isDateRange) {
      if (!this.isDatePickerOpen) {
        this.isFocused = false;
      }
    } else {
      this.isFocused = false;
    }
    this.valueChanged.emit(this.value);
  }

  onFocus() {
    this.isFocused = true;
    if (this.type === 'date' || this.isDateRange) {
      this.openDatePicker();
    }
  }

  openDatePicker() {
    if (!this.isDatePickerOpen) {
      // Add a small delay to prevent immediate closing from document click
      setTimeout(() => {
        this.isDatePickerOpen = true;
        this.isFocused = true;
      }, 50);
    }
  }

  closeDatePicker() {
    this.isDatePickerOpen = false;
    this.isFocused = false;
  }

  onDateSelected(date: Date) {
    this.selectedDate = date;
    this.value = this.formatDate(date);
    if (this.control instanceof FormControl) {
      this.control.setValue(this.value);
    }
    this.onChange(this.value);
    this.dateSelected.emit(date);
    this.valueChanged.emit(this.value);
  }

  onRangeSelected(range: DateRange) {
    this.selectedRange = range;
    this.value = this.formatDateRange(range);
    if (this.control instanceof FormControl) {
      this.control.setValue(this.value);
    }
    this.onChange(this.value);
    this.rangeSelected.emit(range);
    this.valueChanged.emit(this.value);
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDateRange(range: DateRange): string {
    if (!range.startDate || !range.endDate) {
      return '';
    }
    return `${this.formatDate(range.startDate)} - ${this.formatDate(range.endDate)}`;
  }

  getDisplayValue(): string {
    if (this.type === 'date' && this.selectedDate) {
      return this.formatDate(this.selectedDate);
    }
    if (this.isDateRange && this.selectedRange) {
      return this.formatDateRange(this.selectedRange);
    }
    return this.value as string || '';
  }

  // Radio button methods
  isRadioSelected(): boolean {
    if (this.radioValue !== undefined) {
      return this.value === this.radioValue;
    }
    return false;
  }

  selectRadio(): void {
    if (!this.disabled && !this.readonly && !this.isReadOnly) {
      this.value = this.radioValue;
      if (this.control instanceof FormControl) {
        this.control.setValue(this.radioValue);
      }
      this.onChange(this.radioValue);
      this.valueChanged.emit(this.radioValue);
    }
  }

  onRadioClick(event: Event): void {
    event.preventDefault(); // Prevent default radio button behavior

    if (this.disabled || this.readonly || this.isReadOnly) {
      return;
    }

    const wasSelected = this.isRadioSelected();

    if (wasSelected) {
      // Toggle behavior - uncheck if already selected
      this.value = '';
      if (this.control instanceof FormControl) {
        this.control.setValue('');
      }
      this.onChange('');
      this.valueChanged.emit('');
    } else {
      // Normal selection behavior
      this.value = this.radioValue;
      if (this.control instanceof FormControl) {
        this.control.setValue(this.radioValue);
      }
      this.onChange(this.radioValue);
      this.valueChanged.emit(this.radioValue);
    }
  }

  onRadioChange(event: Event): void {
    // This method is kept for compatibility but the main logic is now in onRadioClick
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.checked) {
      this.value = this.radioValue;
      if (this.control instanceof FormControl) {
        this.control.setValue(this.radioValue);
      }
      this.onChange(this.radioValue);
      this.valueChanged.emit(this.radioValue);
    }
  }

  // Switch/Toggle methods
  isSwitchOn(): boolean {
    return !!this.value;
  }

  toggleSwitch(): void {
    if (!this.disabled && !this.readonly && !this.isReadOnly) {
      const newValue = !this.value;
      this.value = newValue;
      if (this.control instanceof FormControl) {
        this.control.setValue(newValue);
      }
      this.onChange(newValue);
      this.valueChanged.emit(newValue);
    }
  }

  onSwitchChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement) {
      const newValue = inputElement.checked;
      this.value = newValue;
      if (this.control instanceof FormControl) {
        this.control.setValue(newValue);
      }
      this.onChange(newValue);
      this.valueChanged.emit(newValue);
    }
  }

  // Number input increment/decrement methods
  incrementNumber(): void {
    if (!this.disabled && !this.readonly && !this.isReadOnly) {
      const currentValue = parseFloat(String(this.value || '0')) || 0;
      const stepValue = this.step === 'any' ? 0.1 : (parseFloat(this.step || '1') || 1);
      const newValue = currentValue + stepValue;

      // Check max constraint
      if (this.max !== undefined && newValue > parseFloat(this.max)) {
        return;
      }

      const formattedValue = this.step === 'any' ? newValue.toFixed(1) : newValue.toString();
      this.value = formattedValue;
      if (this.control instanceof FormControl) {
        this.control.setValue(formattedValue);
      }
      this.onChange(formattedValue);
      this.valueChanged.emit(formattedValue);
    }
  }

  decrementNumber(): void {
    if (!this.disabled && !this.readonly && !this.isReadOnly) {
      const currentValue = parseFloat(String(this.value || '0')) || 0;
      const stepValue = this.step === 'any' ? 0.1 : (parseFloat(this.step || '1') || 1);
      const newValue = currentValue - stepValue;

      // Check min constraint
      if (this.min !== undefined && newValue < parseFloat(this.min)) {
        return;
      }

      const formattedValue = this.step === 'any' ? newValue.toFixed(1) : newValue.toString();
      this.value = formattedValue;
      if (this.control instanceof FormControl) {
        this.control.setValue(formattedValue);
      }
      this.onChange(formattedValue);
      this.valueChanged.emit(formattedValue);
    }
  }

  /** ✅ Handles input change and propagates value */
  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement) {
      let value: any;

      if (this.type === 'checkbox') {
        value = inputElement.checked;

        // ✅ Apply isDisabled logic
        if (this.isReversedCheckBox) {
          value = !value;
        }
      } else {
        value = inputElement.value.trim();
      }

      this.value = value;
      if (this.control instanceof FormControl) {
        this.control.setValue(value);
      }
      this.onChange(value);
      this.toggleChange.emit(value);
    }
  }
  preventIfDate(event: any): void {
    if (this.type === 'date') {
      event.preventDefault();
    }
  }

  // Voice search methods
  startVoiceSearch(): void {
    if (!this.enableVoiceSearch) return;

    // Check if browser supports speech recognition
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.warn('Speech recognition not supported in this browser');
      return;
    }

    this.recognition = new SpeechRecognition();
    this.recognition.continuous = false;
    this.recognition.interimResults = false;
    this.recognition.lang = 'en-US'; // You can make this dynamic based on current language

    this.recognition.onstart = () => {
      this.isListening = true;
      this.voiceSearchStart.emit();
    };

    this.recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      this.value = transcript;
      if (this.control instanceof FormControl) {
        this.control.setValue(transcript);
      }
      this.onChange(transcript);
      this.valueChanged.emit(transcript);
      this.voiceSearchEnd.emit(transcript);
    };

    this.recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error);
      this.isListening = false;
    };

    this.recognition.onend = () => {
      this.isListening = false;
    };

    this.recognition.start();
  }

  stopVoiceSearch(): void {
    if (this.recognition) {
      this.recognition.stop();
      this.isListening = false;
    }
  }

  // Helper methods for RTL padding
  getLeftPadding(): string {
    const isRTL = document.documentElement.dir === 'rtl';

    if (isRTL) {
      // In RTL: left side gets voice icon, right side gets search icon
      if (this.enableVoiceSearch) return '48px'; // 12 * 4 = 48px for icon
      return '12px'; // 3 * 4 = 12px default
    } else {
      // In LTR: left side gets search icon, right side gets voice icon
      if (this.showSearchIcon) return '40px'; // 10 * 4 = 40px for icon
      return '12px'; // 3 * 4 = 12px default
    }
  }

  getRightPadding(): string {
    const isRTL = document.documentElement.dir === 'rtl';

    if (isRTL) {
      // In RTL: right side gets search icon
      if (this.showSearchIcon) return '40px'; // 10 * 4 = 40px for icon
      return '12px'; // 3 * 4 = 12px default
    } else {
      // In LTR: right side gets voice icon
      if (this.enableVoiceSearch) return '48px'; // 12 * 4 = 48px for icon
      return '12px'; // 3 * 4 = 12px default
    }
  }

  // Icon positioning methods
  getSearchIconPosition(): string | null {
    const isRTL = document.documentElement.dir === 'rtl';
    return isRTL ? null : '12px'; // left position for LTR
  }

  getSearchIconRightPosition(): string | null {
    const isRTL = document.documentElement.dir === 'rtl';
    return isRTL ? '12px' : null; // right position for RTL
  }

  getVoiceIconPosition(): string | null {
    const isRTL = document.documentElement.dir === 'rtl';
    return isRTL ? '12px' : null; // left position for RTL
  }

  getVoiceIconRightPosition(): string | null {
    const isRTL = document.documentElement.dir === 'rtl';
    return isRTL ? null : '12px'; // right position for LTR
  }
}


