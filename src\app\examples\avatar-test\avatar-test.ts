import { Component } from '@angular/core';
import { Avatar, AvatarData } from '../../shared/components/avatar/avatar';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'sdga-avatar-test',
  imports: [CommonModule, Avatar],
  templateUrl: './avatar-test.html',
  styleUrl: './avatar-test.scss'
})
export class AvatarTest {
  // Real image paths from assets
  personImagePath = 'assets/images/person.png';
  logoImagePath = 'assets/images/LogoPlaceholder.png';
  invalidImagePath = 'assets/images/nonexistent-image.jpg'; // For testing fallback

  // Sample avatar data for testing
  sampleAvatars: AvatarData[] = [
    {
      name: '<PERSON>',
      initials: 'J<PERSON>',
      src: this.personImagePath,
      alt: '<PERSON> Profile Picture',
      backgroundColor: 'bg-blue-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-green-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      src: this.personImagePath,
      alt: '<PERSON>',
      backgroundColor: 'bg-purple-500'
    },
    {
      name: '<PERSON>',
      initials: '<PERSON><PERSON>',
      backgroundColor: 'bg-pink-500'
    },
    {
      name: 'David Brown',
      initials: 'DB',
      src: this.personImagePath,
      alt: 'David Brown Profile Picture',
      backgroundColor: 'bg-yellow-500'
    },
    {
      name: 'Lisa Davis',
      initials: 'LD',
      backgroundColor: 'bg-red-500'
    }
  ];

  largeAvatarGroup: AvatarData[] = [
    {
      name: 'Alice Cooper',
      initials: 'AC',
      src: this.personImagePath,
      alt: 'Alice Cooper Profile Picture'
    },
    { name: 'Bob Dylan', initials: 'BD' },
    {
      name: 'Charlie Brown',
      initials: 'CB',
      src: this.personImagePath,
      alt: 'Charlie Brown Profile Picture'
    },
    { name: 'Diana Prince', initials: 'DP' },
    { name: 'Edward Norton', initials: 'EN' },
    {
      name: 'Fiona Apple',
      initials: 'FA',
      src: this.personImagePath,
      alt: 'Fiona Apple Profile Picture'
    },
    { name: 'George Lucas', initials: 'GL' },
    { name: 'Helen Hunt', initials: 'HH' }
  ];

  // Sample avatars with mixed content for testing
  mixedContentAvatars: AvatarData[] = [
    {
      name: 'Profile Image User',
      src: this.personImagePath,
      alt: 'User with profile image',
      initials: 'PI'
    },
    {
      name: 'Fallback User',
      src: this.invalidImagePath, // This will fail and show initials
      alt: 'User with fallback to initials',
      initials: 'FU'
    },
    {
      name: 'Icon User',
      icon: 'user',
      alt: 'User with icon avatar'
    },
    {
      name: 'Initials Only',
      initials: 'IO',
      backgroundColor: 'bg-indigo-500',
      alt: 'User with initials only'
    }
  ];
}
