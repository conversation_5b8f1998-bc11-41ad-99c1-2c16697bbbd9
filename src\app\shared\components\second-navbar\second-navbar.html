<div class="flex flex-wrap items-center justify-between w-full p-2 bg-gray-100 border-b border-gray-300 sm:p-3">
  <div class="flex flex-wrap items-center gap-2 text-xs text-gray-700 sm:gap-4 sm:text-sm">
    <div class="flex items-center gap-0.5 sm:gap-1">
      <sdga-svg-icon name="weather"></sdga-svg-icon>
      <span>{{ weather }}</span>
    </div>
    <div class="flex items-center gap-0.5 sm:gap-1">
      <sdga-svg-icon name="calendar"></sdga-svg-icon>
      <span>{{ currentDate }}</span>
    </div>
    <div class="flex items-center gap-0.5 sm:gap-1">
      <sdga-svg-icon name="clock"></sdga-svg-icon>
      <span>{{ currentTime }}</span>
    </div>
    <div class="flex items-center gap-0.5 sm:gap-1">
      <sdga-svg-icon name="location"></sdga-svg-icon>
      <span>{{ city }}</span>
    </div>
  </div>

  <!-- Icons group -->
  <div class="flex items-center gap-2 mt-2 text-gray-500 sm:gap-4 sm:mt-0">
    <!-- Mic toggle -->
    <button (click)="toggleMic()" [class.text-green-600]="micOn" title="Toggle Mic">
      <sdga-svg-icon [name]="micOn ? 'mic-on' : 'mic'"></sdga-svg-icon>
    </button>

    <!-- Zoom In -->
    <button (click)="zoomIn()" title="Zoom In">
      <sdga-svg-icon name="zoom-in"></sdga-svg-icon>
    </button>

    <!-- Zoom Out -->
    <button (click)="zoomOut()" title="Zoom Out">
      <sdga-svg-icon name="zoom-out"></sdga-svg-icon>
    </button>

    <!-- Preview toggle -->
    <button (click)="togglePreview()" [class.text-green-600]="showPreview" title="Toggle Preview">
      <sdga-svg-icon [name]="showPreview ? 'eye' : 'eye-off'"></sdga-svg-icon>
    </button>
  </div>
</div>
