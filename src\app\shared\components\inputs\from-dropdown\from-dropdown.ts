import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, Output, ElementRef, ViewChild, HostListener, OnInit, OnDestroy } from '@angular/core';
import { ReactiveFormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR, FormsModule, AbstractControl, FormControl } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

export interface DropdownOption {
  value: any;
  label: string;
  description?: string;
  icon?: string;
  disabled?: boolean;
  group?: string;
}

export interface DropdownGroup {
  label: string;
  options: DropdownOption[];
}

@Component({
  selector: 'sdga-from-dropdown',
  imports: [CommonModule, TranslateModule, ReactiveFormsModule, FormsModule],
  templateUrl: './from-dropdown.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: FromDropdown,
      multi: true
    }
  ]
})
export class FromDropdown implements <PERSON><PERSON><PERSON>t, OnD<PERSON>roy, ControlValueAccessor {

  // Removed translatedLabel and translatedPlaceholder since translation is handled in template

  // Core inputs
  @Input() name: string | undefined;
  @Input() label: string | undefined;
  @Input() placeholder: string | undefined;
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() loading: boolean = false;

  // Options and data
  @Input() options: DropdownOption[] = [];
  @Input() groups: DropdownGroup[] = [];
  @Input() control: AbstractControl | undefined;
  @Input() formSubmitted: boolean = false;

  // Functionality
  @Input() isMultiSelect: boolean = false;
  @Input() searchable: boolean = false;
  @Input() clearable: boolean = true;
  @Input() maxSelections: number | undefined;

  // Styling
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'filled' = 'default';

  // Legacy support
  @Input() isUseNgValue: boolean = false;
  @Input() viewBy: string | undefined;
  @Input() valueBy: string | undefined;

  @Output() selectionChange = new EventEmitter<any>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();

  // ViewChild references
  @ViewChild('dropdownTrigger', { static: false }) dropdownTrigger!: ElementRef;
  @ViewChild('dropdownPanel', { static: false }) dropdownPanel!: ElementRef;
  @ViewChild('searchInput', { static: false }) searchInput!: ElementRef;

  // Internal state
  subscription: Subscription | undefined;
  dropdownOpen = false;
  searchTerm = '';
  filteredOptions: DropdownOption[] = [];
  filteredGroups: DropdownGroup[] = [];
  selectedValues: any[] = [];
  focusedOptionIndex = -1;
  isFocused = false;

  // ControlValueAccessor implementation
  private _onChange = (_: any) => {};
  private _onTouched = () => {};
  private _controlSubscribed = false;

  constructor(
    private translate: TranslateService,
    private cdr: ChangeDetectorRef,
    private elementRef: ElementRef
  ) {}

  ngOnInit(): void {
    this.initializeTranslations();
    this.initializeOptions();
    this.setupFormControl();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  // Host listeners for outside clicks and keyboard navigation
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.closeDropdown();
    }
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.dropdownOpen) {
      if (event.key === 'Enter' || event.key === ' ' || event.key === 'ArrowDown') {
        event.preventDefault();
        this.openDropdown();
      }
      return;
    }

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        this.closeDropdown();
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.navigateOptions(1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.navigateOptions(-1);
        break;
      case 'Enter':
        event.preventDefault();
        this.selectFocusedOption();
        break;
      case 'Tab':
        this.closeDropdown();
        break;
    }
  }

  // Initialization methods
  private initializeTranslations(): void {
    // Translation is now handled in the template, so no need to translate here
  }

  private initializeOptions(): void {
    this.filteredOptions = [...this.options];
    this.filteredGroups = [...this.groups];
  }

  private setupFormControl(): void {
    if (this.control && !this._controlSubscribed) {
      this.subscription = this.control.valueChanges.subscribe((val) => {
        this.updateSelectedValues(val);
        this._onChange(val);
      });
      this._controlSubscribed = true;
    }
  }

  // Dropdown control methods
  openDropdown(): void {
    if (this.disabled || this.readonly) return;

    this.dropdownOpen = true;
    this.isFocused = true;
    this.focusedOptionIndex = -1;
    this.opened.emit();

    if (this.searchable) {
      setTimeout(() => {
        this.searchInput?.nativeElement?.focus();
      }, 0);
    }
  }

  closeDropdown(): void {
    this.dropdownOpen = false;
    this.isFocused = false;
    this.searchTerm = '';
    this.focusedOptionIndex = -1;
    this.filterOptions();
    this.closed.emit();
    this._onTouched();
  }

  toggleDropdown(): void {
    if (this.dropdownOpen) {
      this.closeDropdown();
    } else {
      this.openDropdown();
    }
  }

  // Navigation methods
  navigateOptions(direction: number): void {
    const availableOptions = this.getAvailableOptions();
    if (availableOptions.length === 0) return;

    this.focusedOptionIndex += direction;

    if (this.focusedOptionIndex < 0) {
      this.focusedOptionIndex = availableOptions.length - 1;
    } else if (this.focusedOptionIndex >= availableOptions.length) {
      this.focusedOptionIndex = 0;
    }
  }

  selectFocusedOption(): void {
    const availableOptions = this.getAvailableOptions();
    if (this.focusedOptionIndex >= 0 && this.focusedOptionIndex < availableOptions.length) {
      const option = availableOptions[this.focusedOptionIndex];
      this.selectOption(option);
    }
  }

  private getAvailableOptions(): DropdownOption[] {
    const allOptions: DropdownOption[] = [];

    // Add ungrouped options
    allOptions.push(...this.filteredOptions.filter(opt => !opt.disabled));

    // Add grouped options
    this.filteredGroups.forEach(group => {
      allOptions.push(...group.options.filter(opt => !opt.disabled));
    });

    return allOptions;
  }

  // Selection methods
  selectOption(option: DropdownOption): void {
    if (option.disabled) return;

    if (this.isMultiSelect) {
      this.toggleMultiSelectOption(option);
    } else {
      this.selectSingleOption(option);
    }
  }

  private selectSingleOption(option: DropdownOption): void {
    const newValue = option.value;
    this.updateFormValue(newValue);
    this.closeDropdown();
  }

  private toggleMultiSelectOption(option: DropdownOption): void {
    const currentValues = this.getCurrentValues();
    const optionIndex = currentValues.findIndex(val => this.compareValues(val, option.value));

    let newValues: any[];
    if (optionIndex === -1) {
      // Add option if not selected and within max limit
      if (!this.maxSelections || currentValues.length < this.maxSelections) {
        newValues = [...currentValues, option.value];
      } else {
        return; // Max selections reached
      }
    } else {
      // Remove option if already selected
      newValues = currentValues.filter((_, index) => index !== optionIndex);
    }

    this.updateFormValue(newValues);
  }

  private updateFormValue(value: any): void {
    if (this.control && this.control instanceof FormControl) {
      this.control.setValue(value);
    }
    this.updateSelectedValues(value);
    this.selectionChange.emit(value);
    this._onChange(value);
  }

  private updateSelectedValues(value: any): void {
    if (this.isMultiSelect) {
      this.selectedValues = Array.isArray(value) ? value : [];
    } else {
      this.selectedValues = value !== null && value !== undefined ? [value] : [];
    }
  }

  private getCurrentValues(): any[] {
    if (this.control?.value) {
      return Array.isArray(this.control.value) ? this.control.value : [this.control.value];
    }
    return [];
  }

  private compareValues(val1: any, val2: any): boolean {
    return val1 === val2;
  }

  // Search and filter methods
  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.filterOptions();
    this.searchChange.emit(this.searchTerm);
    this.focusedOptionIndex = -1;
  }

  private filterOptions(): void {
    if (!this.searchTerm.trim()) {
      this.filteredOptions = [...this.options];
      this.filteredGroups = [...this.groups];
      return;
    }

    const searchLower = this.searchTerm.toLowerCase();

    // Filter ungrouped options
    this.filteredOptions = this.options.filter(option =>
      option.label.toLowerCase().includes(searchLower) ||
      (option.description && option.description.toLowerCase().includes(searchLower))
    );

    // Filter grouped options
    this.filteredGroups = this.groups.map(group => ({
      ...group,
      options: group.options.filter(option =>
        option.label.toLowerCase().includes(searchLower) ||
        (option.description && option.description.toLowerCase().includes(searchLower))
      )
    })).filter(group => group.options.length > 0);
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    this.updateSelectedValues(value);
    if (this.control && value !== this.control.value && this.control instanceof FormControl) {
      this.control.setValue(value, { emitEvent: false });
    }
  }

  registerOnChange(fn: any): void {
    this._onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this._onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (this.control && this.control instanceof FormControl) {
      if (isDisabled) {
        this.control.disable({ emitEvent: false });
      } else {
        this.control.enable({ emitEvent: false });
      }
    }
  }

  // Utility methods
  get formControl(): AbstractControl | null {
    return this.control || null;
  }

  isOptionSelected(option: DropdownOption): boolean {
    return this.selectedValues.some(val => this.compareValues(val, option.value));
  }

  clearSelection(): void {
    const newValue = this.isMultiSelect ? [] : null;
    this.updateFormValue(newValue);
  }

  onSelectionChange(event: any): void {
    // Legacy method for backward compatibility
    this.selectionChange.emit(event);
    this._onTouched();
  }

  onFocus(): void {
    this.isFocused = true;
  }

  onBlur(): void {
    this.isFocused = false;
    this._onTouched();
  }

  // Display methods
  getDisplayText(): string {
    if (this.loading) {
      return this.translate.instant('FORM.LOADING');
    }

    if (!this.selectedValues.length) {
      return this.placeholder || this.translate.instant('PLACEHOLDERS.SELECT_OPTION');
    }

    if (this.isMultiSelect) {
      if (this.selectedValues.length === 1) {
        const option = this.findOptionByValue(this.selectedValues[0]);
        return option ? option.label : this.selectedValues[0];
      }
      return this.translate.instant('FORM.ITEMS_SELECTED', { count: this.selectedValues.length });
    } else {
      const option = this.findOptionByValue(this.selectedValues[0]);
      return option ? option.label : this.selectedValues[0];
    }
  }

  private findOptionByValue(value: any): DropdownOption | undefined {
    // Search in ungrouped options
    let option = this.options.find(opt => this.compareValues(opt.value, value));
    if (option) return option;

    // Search in grouped options
    for (const group of this.groups) {
      option = group.options.find(opt => this.compareValues(opt.value, value));
      if (option) return option;
    }

    return undefined;
  }

  // Size and styling getters
  get sizeClasses(): string {
    switch (this.size) {
      case 'sm':
        return 'h-10 text-sm px-3';
      case 'lg':
        return 'h-14 text-lg px-4';
      default:
        return 'h-12 text-base px-3';
    }
  }

  get hasError(): boolean {
    return !!(this.formSubmitted && this.control?.errors && Object.keys(this.control.errors).length > 0);
  }

  // Legacy support methods
  updateOptions(newOptions: { value: any; label: string }[]): void {
    this.options = newOptions.map(opt => ({
      value: opt.value,
      label: opt.label
    }));
    this.initializeOptions();
    this.cdr.detectChanges();
  }

  toggleOption(optionLabel: string): void {
    // Legacy method - find option by label and toggle
    const option = this.options.find(opt => opt.label === optionLabel);
    if (option) {
      this.selectOption(option);
    }
  }

  isSelected(optionLabel: string): boolean {
    // Legacy method - check if option with label is selected
    const option = this.options.find(opt => opt.label === optionLabel);
    return option ? this.isOptionSelected(option) : false;
  }
}

