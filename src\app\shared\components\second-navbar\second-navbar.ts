import { Component, inject } from '@angular/core';
import { SvgIcon } from "../svg-icon/svg-icon";
import { CommonModule } from '@angular/common';
import { PreviewService } from '../../../core/services/preview.service';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'sdga-second-navbar',
  imports: [SvgIcon, CommonModule],
  templateUrl: './second-navbar.html',
  styleUrl: './second-navbar.scss'
})
export class SecondNavbar {
zoomLevel: number = 100;
micOn: boolean = false;
showPreview: boolean = true;
micStream: MediaStream | null = null;

currentTime: string = '';
  currentDate: string = '';
  city: string;
  weather: string;
  lang: 'en' | 'ar' = 'en'; // You can bind this via @Input later

  private previewService = inject(PreviewService);
  private translate: TranslateService;

  constructor() {
    this.translate = inject(TranslateService);
    this.city = this.translate.instant('FORM.LOADING') || 'Loading...';
    this.weather = this.translate.instant('FORM.LOADING') || 'Loading...';
  }

  ngOnInit(): void {
    this.updateTimeEverySecond();
    this.setDate();
    this.getUserLocation();
    this.setZoomCSS(); // Set initial zoom
    this.translate.onLangChange.subscribe(() => {
      this.city = this.translate.instant('FORM.LOADING') || 'Loading...';
      this.weather = this.translate.instant('FORM.LOADING') || 'Loading...';
      this.getUserLocation();
    });
  }

  private updateTimeEverySecond() {
    setInterval(() => {
      const now = new Date();
      this.currentTime = this.lang === 'ar'
        ? now.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })
        : now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    }, 1000);
  }

  private setDate() {
    const today = new Date();
    this.currentDate = this.lang === 'ar'
      ? today.toLocaleDateString('ar-EG', { day: 'numeric', month: 'long', year: 'numeric' })
      : today.toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' });
  }

  private getUserLocation() {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const lat = position.coords.latitude;
        const lon = position.coords.longitude;
        this.reverseGeocode(lat, lon);
        this.fetchWeather(lat, lon);
      },
      () => {
        this.city = this.lang === 'ar' ? 'غير معروف' : 'Unknown';
        this.weather = this.lang === 'ar' ? 'غير متاح' : 'Unavailable';
      }
    );
  }

  private reverseGeocode(lat: number, lon: number) {
    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`)
      .then(res => res.json())
      .then(data => {
        let city = data.address?.city || data.address?.town || data.address?.state || 'Unknown';
        this.city = this.translate.instant('LOCATION.' + city.toUpperCase()) || city;
      });
  }

  private fetchWeather(lat: number, lon: number) {
    const apiKey = 'YOUR_API_KEY'; // ← Replace with real key from OpenWeatherMap
    fetch(`https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&units=metric&appid=${apiKey}`)
      .then(res => res.json())
      .then(data => {
        const desc = data.weather?.[0]?.main || 'Clear';
        this.weather = this.translate.instant('WEATHER.' + desc) || desc;
      });
  }

  private translateWeatherToArabic(desc: string): string {
    const map: Record<string, string> = {
      Clear: 'صحو',
      Clouds: 'غائم',
      Rain: 'ممطر',
      Snow: 'ثلوج',
      Thunderstorm: 'عاصفة رعدية',
      Drizzle: 'رذاذ',
      Mist: 'ضباب',
    };
    return map[desc] || 'صحو';
  }

  async toggleMic(): Promise<void> {
    if (!this.micOn) {
      try {
        this.micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        this.micOn = true;
        console.log('Mic is now ON');
      } catch (err) {
        this.micOn = false;
        alert('Microphone access denied or not available.');
      }
    } else {
      if (this.micStream) {
        this.micStream.getTracks().forEach(track => track.stop());
        this.micStream = null;
      }
      this.micOn = false;
      console.log('Mic is now OFF');
    }
  }

  zoomOut(): void {
    if (this.zoomLevel < 200) {
      this.zoomLevel += 10;
      this.setZoomCSS();
      console.log('Zoom level:', this.zoomLevel + '%');
    }
  }

  zoomIn(): void {
    if (this.zoomLevel > 50) {
      this.zoomLevel -= 10;
      this.setZoomCSS();
      console.log('Zoom level:', this.zoomLevel + '%');
    }
  }

  private setZoomCSS(): void {
    document.body.style.setProperty('--zoom-level', (this.zoomLevel / 100).toString());
  }

  togglePreview(): void {
    this.showPreview = !this.showPreview;
    this.previewService.setPreview(this.showPreview);
    console.log('Preview is', this.showPreview ? 'VISIBLE' : 'HIDDEN');
  }
}
