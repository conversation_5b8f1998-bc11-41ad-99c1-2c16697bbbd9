
<div class="relative">
    <button class="button button--lg button-neutral me-5" (click)="toggleFilter()">
        <sdga-svg-icon name="filter" svgClass="me-1 flex items-center"></sdga-svg-icon>
        Filter
        <sdga-svg-icon name="arrow-down" svgClass="ms-1 flex items-center"></sdga-svg-icon>
    </button>
    <div class="flex items-center gap-4 mt-4" *ngIf="showFilterItems">
        <span class="chip chip-primary selected">
            Item
            <button class="flex items-center ms-1">
                <sdga-svg-icon name="close-menu"></sdga-svg-icon>
            </button>
        </span>
        <span class="chip chip-primary selected">
            Item
            <button class="flex items-center ms-1">
                <sdga-svg-icon name="close-menu"></sdga-svg-icon>
            </button>
        </span>
        <span class="chip chip-primary selected">
            Item
            <button class="flex items-center ms-1">
                <sdga-svg-icon name="close-menu"></sdga-svg-icon>
            </button>
        </span>
    </div>
    <div class="filter-content" *ngIf="isFilterOpened">
        <!-- <p (click)="showFilterItems = !showFilterItems">Filter Content</p> -->
      <form [formGroup]="testInputs" (ngSubmit)="onSubmit()" class="space-y-6">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <sdga-form-input
            name="radioGroup"
            type="radio"
            [label]="'FORM_LABELS.RADIO_OPTION_1' | translate"
            [control]="testInputs.get('radioGroup')!"
            [formSubmitted]="formSubmitted"
            radioValue="option1"
            radioSize="lg"
          ></sdga-form-input>
        </div>
      </form>
    </div>
</div>