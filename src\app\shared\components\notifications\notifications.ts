import { Component, Input } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'sdga-notifications',
  imports: [SvgIcon, CommonModule],
  templateUrl: './notifications.html',
  styleUrl: './notifications.scss',
})
export class Notifications {
  iconClass: string = ' inline-block  p-3 rounded-full';
  @Input() notificationType: 'success' | 'error' | 'info' | 'warning' = 'warning';
  @Input() isVertical: boolean = true;
  @Input() iconName: string = 'successIcon';
  @Input() ButtonClose: string = 'ButtonClose';
  @Input() closeIconClass: string = '';
  @Input() title: string = '';
  @Input() content: string ='';
  @Input() buttons: { label: string; onClick: () => void }[] = [];
  get containerClasses(): string[] {
    const classes = ['flex', 'rounded-md', 'py-4', 'px-6' , 'bg-white'];
    const verticalCalss = this.isVertical
      ? ['border-s-[8px]']
      : ['border-t-[8px]', ' flex-col'];
    switch (this.notificationType) {
      case 'success':
        classes.push('border-success-600');
        this.iconClass += ' text-success-600 bg-success-50 ';
        break;
      case 'error':
        classes.push('border-error-600');
        this.iconClass += ' text-error-600 bg-error-50 ';
        break;
      case 'info':
        classes.push('border-blue-500');
        this.iconClass += ' text-info-700 bg-info-light ';
        break;
         case 'warning':
        classes.push('border-warning-500');
        this.iconClass += ' text-warning-700 bg-warning-50 ';
        break;
    }
    const noticationsStyles = [...classes, ...verticalCalss];
    return noticationsStyles;
  }
  get contentClasses(): string[] {
    return ['flex-1', 'text-gray-500', 'px-5'];
  }
   get contentTitleClasse(): string[] {
    return [ 'font-semibold' , 'text-gray-800' , 'text-base'];
  }
  get TextContent(): string[] {
    return [ 'font-normal' , 'text-gray-700'];
  }
  get actionsClasses(): string[] {
    return ['mt-2', 'flex', 'gap-2'];
  }
}
