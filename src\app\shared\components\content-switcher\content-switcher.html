<div class="flex flex-wrap items-center w-full overflow-hidden rounded-md">
  <button *ngFor="let item of items; let i = index" (click)="select(i)" [ngClass]="[
      sizeClasses[size || 'sm'],
      selectedIndex === i ? hasBackground ? hasbgActiveClasses : activeClasses : hasBackground ? hasbgInactiveClasses : inactiveClasses,
    ]">
    {{ item }}
  </button>
</div>

<!-- 'transition-colors duration-150 font-medium focus:outline-none' -->