<!-- Tailwind usage -->
<div class="p-4 rounded-lg bg-sa-500 text-gray-25">
  Saudi Government Primary Section
</div>

<div class="p-4 rounded bg-lavender-100 text-lavender-800">
  Welcome to the calm zone ✨
</div>

<div class="p-4 border-l-4 rounded bg-error-50 text-error-800 border-error-500">
  <strong>Error:</strong> Please check the form for validation issues.
</div>

<div class="p-4 border-l-4 rounded bg-warning-50 text-warning-800 border-warning-500">
  ⚠️ Please double-check the information you entered.
</div>

<div class="p-4 border-l-4 rounded bg-info-50 text-info-800 border-info-500">
  💡 Your report is being generated. Please wait a few moments.
</div>

<div class="p-4 border-l-4 rounded bg-success-50 text-success-800 border-success-500">
  ✅ Operation completed successfully.
</div>

<div class="p-4 text-white rounded bg-gradient-sa-01">
  Beautiful Saudi Green Gradient!
</div>

<!-- Light background -->
<p class="text-primary-light">Primary text on light</p>
<p class="text-secondary-light">Secondary text on light</p>

<!-- Dark background -->
<div class="p-4 bg-gray-900">
  <p class="text-primary-dark">Primary text on dark</p>
  <p class="text-secondary-dark">Secondary text on dark</p>
</div>

<!-- Platforms branding -->
<p class="text-sa-primary">SA Primary</p>

<!-- Semantic usage -->
<p class="text-error-primary">This is an error message.</p>
<h1 class="font-bold font-arabic text-display-md md:text-display-xl lg:text-display-2xl">
  عنوان متجاوب
</h1>

<div class="shadow-sa-3xl backdrop-blur-sa-xl">...</div>

<div class="shadow-sa-3xl backdrop-blur-sa-xl">...</div>

<div class="mx-auto mt-8 bg-gray-100 p-container-desktop max-w-container-desktop">
  <h1 class="text-2xl font-bold mb-xl">Tailwind Spacing & Width Test</h1>

  <div class="bg-white rounded-lg shadow-md p-xl">
    <p class="text-gray-700 mb-md max-w-paragraph">
      This paragraph uses <strong>max-w-paragraph</strong> and
      <strong>mb-md</strong> to test custom Tailwind spacing and widths. You
      should see consistent spacing and readable line length if everything is
      configured correctly.
    </p>

    <div class="flex flex-wrap gap-3xl">
      <div class="flex items-center justify-center w-40 h-20 text-white bg-blue-500 rounded-md">
        Box 1
      </div>
      <div class="flex items-center justify-center w-40 h-20 text-white bg-green-500 rounded-md">
        Box 2
      </div>
      <div class="flex items-center justify-center w-40 h-20 text-white bg-purple-500 rounded-md">
        Box 3
      </div>
    </div>
  </div>
</div>

<div class="max-w-xl mx-auto p-xl space-y-lg">
  <div class="bg-sa-200 p-md radius-none">radius-none</div>
  <div class="bg-sa-300 p-md rounded-xs">radius-xs</div>
  <div class="rounded-sm bg-sa-400 p-md">radius-sm</div>
  <div class="text-white rounded-md bg-sa-500 p-md">radius-md</div>
  <div class="text-white rounded-lg bg-sa-600 p-md">radius-lg</div>
  <div class="text-white bg-sa-700 p-md rounded-xl">radius-xl</div>
  <div class="text-white rounded-full bg-sa-800 p-md">radius-full</div>
</div>

<!-- 12-column layout -->
<div class="grid grid-cols-12 gap-gutter-desktop">
  <div class="col-span-4">Left</div>
  <div class="col-span-8">Right</div>
</div>

<!-- 3-column layout -->
<div class="grid grid-cols-3 gap-gutter-tablet">
  <div>Col 1</div>
  <div>Col 2</div>
  <div>Col 3</div>
</div>

<section class="container py-10 mx-auto bg-gray-50">
  <div class="grid grid-cols-12 mx-auto gap-gutter-desktop max-w-container-desktop">
    <div class="col-span-3 p-4 bg-blue-100">Sidebar</div>
    <div class="col-span-9 p-4 bg-blue-200">Main content</div>
  </div>
</section>

<div class="container">
  <div class="container">
    <h1 class="my-lg">Test Shared Tailwind classes</h1>

    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
      <div class="card">hello</div>

      <div class="skeletonContainer">
        skeleton
        <div class="w-32 h-32 my-2">
          <div class="skeleton circleSkeleton"></div>
        </div>

        <div class="w-32 h-32 my-2">
          <div class="skeleton squareSkeleton"></div>
        </div>

        <div class="h-8 my-2 w-72">
          <div class="skeleton squareSkeleton"></div>
        </div>

        <div class="h-20 my-2 w-52">
          <div class="skeleton squareSkeleton"></div>
        </div>

        <div class="w-16 my-2 h-44">
          <div *ngIf="isLoadingLocal" class="skeleton squareSkeleton">
            Loading...
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="text-2xl font-bold text-sa-600">Links Classes</div>
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-base font-normal leading-6 text-sa-500">Link</a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-400">Link</a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-lighter-100">Link</a>
    <div>
      <a href="#" class="text-base font-normal leading-6 border-2 text-sa-600 border-default-100">Link</a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-sa-800">Link</a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400">Link</a>
    <a href="#" class="text-base font-normal leading-6 text-sa-500">رابط</a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-400">رابط</a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-lighter-100">رابط</a>
    <div>
      <a href="#" class="text-base font-normal leading-6 border-2 text-sa-600 border-default-100">رابط</a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-sa-800">رابط</a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400">رابط</a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-sm font-normal leading-6 text-sa-500">Link</a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-400">Link</a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-lighter-100">Link</a>
    <div>
      <a href="#" class="text-sm font-normal leading-6 border-2 text-sa-600 border-default-100">Link</a>
    </div>
    <a href="#" class="text-sm font-normal leading-6 text-sa-800">Link</a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-400">Link</a>
    <a href="#" class="text-sm font-normal leading-6 text-sa-500">رابط</a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-400">رابط</a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-lighter-100">رابط</a>
    <div>
      <a href="#" class="text-sm font-normal leading-6 border-2 text-sa-600 border-default-100">رابط</a>
    </div>
    <a href="#" class="text-sm font-normal leading-6 text-sa-800">رابط</a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-400">رابط</a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-base font-normal leading-6 underline text-sa-500">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-400">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-lighter-100">Link
    </a>
    <div>
      <a href="#" class="text-base font-normal leading-6 underline border-2 text-sa-600 border-default-100">Link
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-800">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-500">رابط</a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-400">رابط</a>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-lighter-100">رابط</a>
    <div>
      <a href="#" class="text-base font-normal leading-6 underline border-2 text-sa-600 border-default-100">رابط</a>
    </div>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-800">رابط</a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">رابط</a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-500">Link
    </a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-400">Link</a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-lighter-100">Link</a>
    <div>
      <a href="#" class="text-sm font-normal leading-6 underline border-2 text-sa-600 border-default-100">Link
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-800">Link
    </a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-400 underline">Link
    </a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-500">رابط</a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-400">رابط</a>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-lighter-100">رابط</a>
    <div>
      <a href="#" class="text-sm font-normal leading-6 underline border-2 text-sa-600 border-default-100">رابط</a>
    </div>
    <a href="#" class="text-sm font-normal leading-6 underline text-sa-800">رابط</a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-400 underline">رابط</a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-base font-normal leading-6 text-gray-700">Link </a>

    <a href="#" class="text-base font-normal leading-6 text-gray-500 underline">Link</a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">Link</a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-gray-700 border-2 border-default-100">Link
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-sa-800">Link </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400">Link </a>

    <a href="#" class="text-base font-normal leading-6 text-gray-700">رابط </a>

    <a href="#" class="text-base font-normal leading-6 text-gray-500 underline">رابط</a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">رابط</a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-gray-700 border-2 border-default-100">رابط
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-sa-800">رابط </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400">رابط </a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-sm font-normal leading-6 text-gray-700">Link </a>

    <a href="#" class="text-sm font-normal leading-6 text-gray-500 underline">Link</a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-400 underline">Link</a>
    <div>
      <a href="#" class="text-sm font-normal leading-6 text-gray-700 border-2 border-default-100">Link
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 text-sa-800">Link </a>
    <a href="#" class="text-sm font-normal leading-5 text-gray-400">Link </a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-700">رابط </a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-500 underline">رابط</a>
    <a href="#" class="text-sm font-normal leading-6 text-gray-400 underline">رابط</a>
    <div>
      <a href="#" class="text-sm font-normal leading-6 text-gray-700 border-2 border-default-100">رابط
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 text-sa-800">رابط </a>
    <a href="#" class="text-sm font-normal leading-5 text-gray-400">رابط </a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-base font-normal leading-6 text-gray-700 underline">Link
    </a>

    <a href="#" class="text-base font-normal leading-6 text-gray-500 underline">Link
    </a>

    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">Link</a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-gray-700 underline border-2 border-default-100">Link
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-800">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-700 underline">رابط
    </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-500 underline">رابط
    </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">رابط</a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-gray-700 underline border-2 border-default-100">رابط
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 underline text-sa-800">رابط
    </a>
    <a href="#" class="text-base font-normal leading-6 text-gray-400 underline">رابط
    </a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-white">
    <a href="#" class="text-sm font-normal leading-5 text-gray-700 underline">Link
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-gray-500 underline">Link
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-gray-400 underline">Link
    </a>

    <div>
      <a href="#" class="text-sm font-normal leading-5 text-gray-700 underline border-2 border-default-100">Link
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 underline text-sa-800">Link
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-gray-400 underline">Link
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-gray-700 underline">رابط
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-gray-500 underline">رابط
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-gray-400 underline">رابط
    </a>

    <div>
      <a href="#" class="text-sm font-normal leading-5 text-gray-700 underline border-2 border-default-100">رابط
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 underline text-sa-800">رابط
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-gray-400 underline">رابط
    </a>
  </div>

  <div class="grid grid-cols-6 gap-4 p-4 bg-sa-800">
    <a href="#" class="text-base font-normal leading-6 text-white">Link </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-80">Link
    </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-60">Link
    </a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-white border-2 border-white">Link
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-white opacity-90">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 text-white opacity-30">Link
    </a>

    <a href="#" class="text-base font-normal leading-6 text-white">رابط </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-80">رابط
    </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-60">رابط
    </a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-white border-2 border-white">رابط
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-white opacity-90">رابط
    </a>
    <a href="#" class="text-base font-normal leading-6 text-white opacity-30">رابط
    </a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-sa-800">
    <a href="#" class="text-sm font-normal leading-5 text-white">Link </a>

    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-80">Link
    </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-60">Link
    </a>
    <div>
      <a href="#" class="text-sm font-normal leading-5 text-white border-2 border-white">Link
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 text-white opacity-90">Link
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-white opacity-30">Link
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-white">رابط </a>

    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-80">رابط
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-60">رابط
    </a>
    <div>
      <a href="#" class="text-sm font-normal leading-5 text-white border-2 border-white">رابط
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 text-white opacity-90">رابط
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-white opacity-30">رابط
    </a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-sa-800">
    <a href="#" class="text-base font-normal leading-6 text-white">Link </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-80">Link
    </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-60">Link
    </a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-white underline border-2 border-white">Link
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-90">Link
    </a>
    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-30">Link
    </a>

    <a href="#" class="text-base font-normal leading-6 text-white">رابط </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-80">رابط
    </a>

    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-60">رابط
    </a>
    <div>
      <a href="#" class="text-base font-normal leading-6 text-white underline border-2 border-white">رابط
      </a>
    </div>
    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-90">رابط
    </a>
    <a href="#" class="text-base font-normal leading-6 text-white underline opacity-30">رابط
    </a>
  </div>
  <hr />
  <div class="grid grid-cols-6 gap-4 p-4 bg-sa-800">
    <a href="#" class="text-sm font-normal leading-5 text-white">Link </a>
    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-80">Link
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-60">Link
    </a>
    <div>
      <a href="#" class="text-sm font-normal leading-5 text-white underline border-2 border-white">Link
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-90">Link
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-30">Link
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-white">رابط </a>
    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-80">رابط
    </a>

    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-60">رابط
    </a>
    <div>
      <a href="#" class="text-sm font-normal leading-5 text-white underline border-2 border-white">رابط
      </a>
    </div>
    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-90">رابط
    </a>
    <a href="#" class="text-sm font-normal leading-5 text-white underline opacity-30">رابط
    </a>
  </div>
  <hr />

  <div class="text-2xl font-bold text-sa-600">Lists Classes</div>
  <div class="grid grid-cols-6 gap-4 p-4 ">
    <ol class="ml-5 list-decimal text-sa-600">
      <li>List item</li>
      <ol class="list-[lower-alpha] ml-5">
        <li>List item</li>
        <li>List item</li>
        <li>List item</li>
      </ol>
    </ol>

    <ul class="list-[square] pl-4 text-sa-600">
      <li class="list-none">
        - List item
        <ul class="pl-6 list-disc">
          <li>List item</li>
          <li>List item</li>
          <li>List item</li>
        </ul>
      </li>
    </ul>

    <ul class="list-[square] pl-4 text-sa-600">
      <li class="list-none">
        <span class="inline-block"><sdga-svg-icon [name]="'greenMarkList'"
            [svgClass]="'iconClass'"></sdga-svg-icon></span>
        <span class="mx-2">List item</span>
        <ul class="pl-6">
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>

        </ul>
      </li>
    </ul>


    <ol class="ml-5 list-decimal text-sa-600">
      <li>عنصر قائمة</li>
      <ol class="list-[arabic-indic] list-inside rtl ml-5">
        <li>عنصر قائمة</li>
        <li>عنصر قائمة</li>
        <li>عنصر قائمة</li>
      </ol>
    </ol>

    <ul class="list-[square] pl-4 text-sa-600">
      <li class="list-none">
        - عنصر قائمة
        <ul class="pl-6 list-disc">
          <li>عنصر قائمة</li>
          <li>عنصر قائمة</li>
          <li>عنصر قائمة</li>
        </ul>
      </li>
    </ul>

    <ul class="list-[square] pl-4 text-sa-600">
      <li class="list-none">
        <span class="inline-block"><sdga-svg-icon [name]="'greenMarkList'"
            [svgClass]="'iconClass'"></sdga-svg-icon></span>
        <span class="mx-2">عنصر قائمة</span>
        <ul class="pl-6">
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>

        </ul>
      </li>
    </ul>

    <ol class="ml-5 list-decimal text-default-100">
      <li>List item</li>
      <ol class="list-[lower-alpha] ml-5">
        <li>List item</li>
        <li>List item</li>
        <li>List item</li>
      </ol>
    </ol>

    <ul class="list-[square] pl-4 text-default-100">
      <li class="list-none">
        - List item
        <ul class="pl-6 list-disc">
          <li>List item</li>
          <li>List item</li>
          <li>List item</li>
        </ul>
      </li>
    </ul>

    <ul class="list-[square] pl-4 text-default-100">
      <li class="list-none">
        <span class="inline-block"><sdga-svg-icon [name]="'greenMarkList'"
            [svgClass]="'iconClass'"></sdga-svg-icon></span>
        <span class="mx-2">List item</span>
        <ul class="pl-6">
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>

        </ul>
      </li>
    </ul>



    <ol class="ml-5 list-decimal text-default-100">
      <li>عنصر قائمة</li>
      <ol class="list-[arabic-indic] list-inside rtl ml-5">
        <li>عنصر قائمة</li>
        <li>عنصر قائمة</li>
        <li>عنصر قائمة</li>
      </ol>
    </ol>

    <ul class="list-[square] pl-4 text-default-100">
      <li class="list-none">
        - عنصر قائمة
        <ul class="pl-6 list-disc">
          <li>عنصر قائمة</li>
          <li>عنصر قائمة</li>
          <li>عنصر قائمة</li>
        </ul>
      </li>
    </ul>

    <ul class="list-[square] pl-4 text-default-100">
      <li class="list-none">
        <span class="inline-block"><sdga-svg-icon [name]="'greenMarkList'"
            [svgClass]="'iconClass'"></sdga-svg-icon></span>
        <span class="mx-2">عنصر قائمة</span>
        <ul class="pl-6">
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>

        </ul>
      </li>
    </ul>

  </div>

  <div class="grid grid-cols-6 gap-4 p-4 bg-sa-800">
    <ol class="ml-5 text-white list-decimal">
      <li>List item</li>
      <ol class="list-[lower-alpha] ml-5">
        <li>List item</li>
        <li>List item</li>
        <li>List item</li>
      </ol>
    </ol>
    <ul class="list-[square] pl-4 text-white">
      <li class="list-none">
        - List item
        <ul class="pl-6 list-disc">
          <li>List item</li>
          <li>List item</li>
          <li>List item</li>
        </ul>
      </li>
    </ul>
    <ul class="list-[square] pl-4 text-white">
      <li class="list-none">
        <span class="inline-block"><sdga-svg-icon [name]="'greenMarkList'"
            [svgClass]="'iconClass'"></sdga-svg-icon></span>
        <span class="mx-2">List item</span>
        <ul class="pl-6">
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>List item</span>
          </li>

        </ul>
      </li>
    </ul>



    <ol class="ml-5 text-white list-decimal">
      <li>عنصر قائمة</li>
      <ol class="list-[arabic-indic] list-inside rtl ml-5">
        <li>عنصر قائمة</li>
        <li>عنصر قائمة</li>
        <li>عنصر قائمة</li>
      </ol>
    </ol>

    <ul class="list-[square] pl-4 text-white">
      <li class="list-none">
        - عنصر قائمة
        <ul class="pl-6 list-disc">
          <li>عنصر قائمة</li>
          <li>عنصر قائمة</li>
          <li>عنصر قائمة</li>
        </ul>
      </li>
    </ul>

    <ul class="list-[square] pl-4 text-white">
      <li class="list-none">
        <span class="inline-block"><sdga-svg-icon [name]="'greenMarkList'"
            [svgClass]="'iconClass'"></sdga-svg-icon></span>
        <span class="mx-2">عنصر قائمة</span>
        <ul class="pl-6">
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="inline-block mt-1"><sdga-svg-icon [name]="'greenMarkList'"
                [svgClass]="'iconClass'"></sdga-svg-icon></span>
            <span>عنصر قائمة</span>
          </li>

        </ul>
      </li>
    </ul>
  </div>
  <sdga-navbar></sdga-navbar>
</div>
<sdga-navbar></sdga-navbar>

<!-- ========================================= -->
<!-- SDGA TOOLTIP COMPONENT EXAMPLES -->
<!-- ========================================= -->

<div class="mt-12 space-y-8">
  <h2 class="mb-6 font-bold text-gray-900 text-display-lg">SDGA Tooltip Components</h2>

  <!-- Tooltip Examples Container -->
  <div class="p-8 space-y-6 bg-gray-100 rounded-lg">

    <!-- Light Theme Tooltips Column -->
    <div class="space-y-4">
      <h3 class="mb-4 text-lg font-semibold text-gray-800">Light & Dark Theme Tooltips</h3>

      <div class="grid grid-cols-2 gap-6">
        <!-- Light Theme Column -->
        <div class="space-y-4">

          <!-- Light Tooltip 1 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div
                class="flex items-center justify-center w-6 h-6 text-gray-600 bg-gray-300 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Tooltip Content -->
              <div
                class="absolute top-0 z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 left-8 group-hover:visible group-hover:opacity-100">
                <!-- Arrow pointing left positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent right-full top-4 border-r-white">
                </div>

                <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-lg w-60">
                  <div class="mb-1 text-sm font-semibold text-gray-900">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-600">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

          <!-- Light Tooltip 2 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div
                class="flex items-center justify-center w-6 h-6 text-gray-600 bg-gray-300 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Tooltip Content with arrow on top -->
              <div
                class="absolute top-0 z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 left-8 group-hover:visible group-hover:opacity-100">
                <!-- Arrow pointing up positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-b-[6px] border-l-[6px] border-r-[6px] border-transparent bottom-full left-8 border-b-white">
                </div>

                <div class="relative p-3 bg-white border border-gray-200 rounded-lg shadow-lg w-60">
                  <div class="mb-1 text-sm font-semibold text-gray-900">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-600">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

          <!-- Light Tooltip 3 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div
                class="flex items-center justify-center w-6 h-6 text-gray-600 bg-gray-300 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Tooltip Content with arrow on bottom -->
              <div
                class="absolute z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 left-8 -top-20 group-hover:visible group-hover:opacity-100">
                <div class="relative p-3 bg-white border border-gray-200 rounded-lg shadow-lg w-60">
                  <div class="mb-1 text-sm font-semibold text-gray-900">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-600">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
                <!-- Arrow pointing down positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-t-[6px] border-l-[6px] border-r-[6px] border-transparent top-full left-8 border-t-white">
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

          <!-- Light Tooltip 4 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div
                class="flex items-center justify-center w-6 h-6 text-gray-600 bg-gray-300 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Tooltip Content with arrow on right -->
              <div
                class="absolute top-0 z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 -left-64 group-hover:visible group-hover:opacity-100">
                <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-lg w-60">
                  <div class="mb-1 text-sm font-semibold text-gray-900">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-600">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
                <!-- Arrow pointing right positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-t-[6px] border-b-[6px] border-l-[6px] border-transparent left-full top-4 border-l-white">
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

        </div>

        <!-- Dark Theme Column -->
        <div class="space-y-4">

          <!-- Dark Tooltip 1 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div class="flex items-center justify-center w-6 h-6 text-white bg-gray-700 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Dark Tooltip Content -->
              <div
                class="absolute top-0 z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 left-8 group-hover:visible group-hover:opacity-100">
                <!-- Arrow pointing left positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent right-full top-3 border-r-gray-800">
                </div>

                <div class="p-3 bg-gray-800 rounded-lg shadow-xl w-60">
                  <div class="mb-1 text-sm font-semibold text-white">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-300">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

          <!-- Dark Tooltip 2 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div class="flex items-center justify-center w-6 h-6 text-white bg-gray-700 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Dark Tooltip Content with arrow on top -->
              <div
                class="absolute top-0 z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 left-8 group-hover:visible group-hover:opacity-100">
                <!-- Arrow pointing up positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-b-[6px] border-l-[6px] border-r-[6px] border-transparent bottom-full left-6 border-b-gray-800">
                </div>

                <div class="relative p-3 bg-gray-800 rounded-lg shadow-xl w-60">
                  <div class="mb-1 text-sm font-semibold text-white">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-300">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

          <!-- Dark Tooltip 3 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div class="flex items-center justify-center w-6 h-6 text-white bg-gray-700 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Dark Tooltip Content with arrow on bottom -->
              <div
                class="absolute z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 left-8 -top-20 group-hover:visible group-hover:opacity-100">
                <div class="relative p-3 bg-gray-800 rounded-lg shadow-xl w-60">
                  <div class="mb-1 text-sm font-semibold text-white">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-300">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
                <!-- Arrow pointing down positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-t-[6px] border-l-[6px] border-r-[6px] border-transparent top-full left-6 border-t-gray-800">
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

          <!-- Dark Tooltip 4 -->
          <div class="flex items-start gap-3">
            <div class="relative group">
              <div class="flex items-center justify-center w-6 h-6 text-white bg-gray-700 rounded-full cursor-pointer">
                <span class="text-xs font-medium">?</span>
              </div>

              <!-- Dark Tooltip Content with arrow on right -->
              <div
                class="absolute top-0 z-50 invisible transition-opacity duration-200 ease-in-out opacity-0 -left-64 group-hover:visible group-hover:opacity-100">
                <div class="p-3 bg-gray-800 rounded-lg shadow-xl w-60">
                  <div class="mb-1 text-sm font-semibold text-white">Tooltip title</div>
                  <div class="text-xs leading-relaxed text-gray-300">Max width of tooltips is 240px - text will wrap
                    automatically</div>
                </div>
                <!-- Arrow pointing right positioned outside tooltip -->
                <div
                  class="absolute w-0 h-0 border-t-[6px] border-b-[6px] border-l-[6px] border-transparent left-full top-3 border-l-gray-800">
                </div>
              </div>
            </div>
            <div class="text-sm text-gray-700">Tooltip title</div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- ========================================= -->
<!-- SDGA LOADER COMPONENT EXAMPLES -->
<!-- ========================================= -->

<div class="mt-12 space-y-8">
  <h2 class="mb-6 font-bold text-gray-900 text-display-lg">SDGA Loader Components</h2>

  <!-- Loader Examples Container -->
  <div class="p-8 space-y-8 bg-gray-100 rounded-lg">

    <!-- Size Variations -->
    <div class="space-y-6">
      <h3 class="mb-4 text-lg font-semibold text-gray-800">Size Variations</h3>

      <div class="grid grid-cols-3 gap-8">
        <!-- Small Loaders -->
        <div class="space-y-4 text-center">
          <h4 class="text-sm font-medium text-gray-700">Small (16px)</h4>
          <div class="flex items-center justify-center gap-4">
            <!-- Small Black Loader -->
            <div class="w-4 h-4 border-2 border-gray-300 rounded-full border-t-gray-900 animate-spin"></div>
            <!-- Small Green Loader -->
            <div class="w-4 h-4 border-2 border-gray-300 rounded-full border-t-green-600 animate-spin"></div>
            <!-- Small White Loader (on dark background) -->
            <div class="p-2 bg-gray-800 rounded">
              <div class="w-4 h-4 border-2 border-gray-600 rounded-full border-t-white animate-spin"></div>
            </div>
          </div>
        </div>

        <!-- Medium Loaders -->
        <div class="space-y-4 text-center">
          <h4 class="text-sm font-medium text-gray-700">Medium (24px)</h4>
          <div class="flex items-center justify-center gap-4">
            <!-- Medium Black Loader -->
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-gray-900 animate-spin"></div>
            <!-- Medium Green Loader -->
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-green-600 animate-spin"></div>
            <!-- Medium White Loader (on dark background) -->
            <div class="p-2 bg-gray-800 rounded">
              <div class="w-6 h-6 border-2 border-gray-600 rounded-full border-t-white animate-spin"></div>
            </div>
          </div>
        </div>

        <!-- Large Loaders -->
        <div class="space-y-4 text-center">
          <h4 class="text-sm font-medium text-gray-700">Large (32px)</h4>
          <div class="flex items-center justify-center gap-4">
            <!-- Large Black Loader -->
            <div class="w-8 h-8 border-gray-300 rounded-full border-3 border-t-gray-900 animate-spin"></div>
            <!-- Large Green Loader -->
            <div class="w-8 h-8 border-gray-300 rounded-full border-3 border-t-green-600 animate-spin"></div>
            <!-- Large White Loader (on dark background) -->
            <div class="p-3 bg-gray-800 rounded">
              <div class="w-8 h-8 border-gray-600 rounded-full border-3 border-t-white animate-spin"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Color Variations -->
    <div class="space-y-6">
      <h3 class="mb-4 text-lg font-semibold text-gray-800">Color Variations</h3>

      <div class="grid grid-cols-3 gap-8">
        <!-- Black/Dark Theme -->
        <div class="space-y-4 text-center">
          <h4 class="text-sm font-medium text-gray-700">Black/Dark</h4>
          <div class="flex items-center justify-center gap-4">
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-gray-900 animate-spin"></div>
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-gray-800 animate-spin"></div>
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-gray-700 animate-spin"></div>
          </div>
        </div>

        <!-- Green/Success Theme -->
        <div class="space-y-4 text-center">
          <h4 class="text-sm font-medium text-gray-700">Green/Success</h4>
          <div class="flex items-center justify-center gap-4">
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-green-600 animate-spin"></div>
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-green-500 animate-spin"></div>
            <div class="w-6 h-6 border-2 border-gray-300 rounded-full border-t-emerald-600 animate-spin"></div>
          </div>
        </div>

        <!-- White/Light Theme -->
        <div class="space-y-4 text-center">
          <h4 class="text-sm font-medium text-gray-700">White/Light</h4>
          <div class="flex items-center justify-center gap-4">
            <div class="p-2 bg-gray-800 rounded">
              <div class="w-6 h-6 border-2 border-gray-600 rounded-full border-t-white animate-spin"></div>
            </div>
            <div class="p-2 bg-gray-700 rounded">
              <div class="w-6 h-6 border-2 border-gray-500 rounded-full border-t-gray-100 animate-spin"></div>
            </div>
            <div class="p-2 bg-green-800 rounded">
              <div class="w-6 h-6 border-2 border-green-600 rounded-full border-t-white animate-spin"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Examples -->
    <div class="space-y-6">
      <h3 class="mb-4 text-lg font-semibold text-gray-800">Usage Examples</h3>

      <div class="grid grid-cols-2 gap-8">
        <!-- Button with Loader -->
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700">Button Loading States</h4>
          <div class="space-y-3">
            <!-- Primary Button Loading -->
            <button
              class="flex items-center justify-center gap-2 px-4 py-2 text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50"
              disabled>
              <div class="w-4 h-4 border-2 border-green-300 rounded-full border-t-white animate-spin"></div>
              Loading...
            </button>

            <!-- Secondary Button Loading -->
            <button
              class="flex items-center justify-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              disabled>
              <div class="w-4 h-4 border-2 border-gray-300 rounded-full border-t-gray-700 animate-spin"></div>
              Processing...
            </button>
          </div>
        </div>

        <!-- Page/Content Loading -->
        <div class="space-y-4">
          <h4 class="text-sm font-medium text-gray-700">Page Loading</h4>
          <div class="space-y-3">
            <!-- Centered Page Loader -->
            <div class="flex items-center justify-center p-8 bg-white border border-gray-200 rounded-lg">
              <div class="space-y-3 text-center">
                <div class="w-8 h-8 mx-auto border-gray-300 rounded-full border-3 border-t-green-600 animate-spin">
                </div>
                <p class="text-sm text-gray-600">Loading content...</p>
              </div>
            </div>

            <!-- Inline Content Loader -->
            <div class="flex items-center gap-3 p-4 rounded-lg bg-gray-50">
              <div class="w-5 h-5 border-2 border-gray-300 rounded-full border-t-gray-600 animate-spin"></div>
              <span class="text-sm text-gray-600">Fetching data...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Guide -->
    <div class="space-y-6">
      <h3 class="mb-4 text-lg font-semibold text-gray-800">Configuration Guide</h3>

      <div class="p-6 bg-white border border-gray-200 rounded-lg">
        <h4 class="mb-4 font-medium text-gray-800 text-md">Customizable Classes</h4>

        <div class="space-y-4">
          <!-- Size Configuration -->
          <div class="grid grid-cols-2 gap-6">
            <div>
              <h5 class="mb-2 text-sm font-medium text-gray-700">Size Classes</h5>
              <div class="space-y-1 text-xs text-gray-600">
                <div><code class="px-2 py-1 bg-gray-100 rounded">w-4 h-4</code> - Small (16px)</div>
                <div><code class="px-2 py-1 bg-gray-100 rounded">w-6 h-6</code> - Medium (24px)</div>
                <div><code class="px-2 py-1 bg-gray-100 rounded">w-8 h-8</code> - Large (32px)</div>
                <div><code class="px-2 py-1 bg-gray-100 rounded">w-10 h-10</code> - Extra Large (40px)</div>
              </div>
            </div>

            <div>
              <h5 class="mb-2 text-sm font-medium text-gray-700">Border Width</h5>
              <div class="space-y-1 text-xs text-gray-600">
                <div><code class="px-2 py-1 bg-gray-100 rounded">border-2</code> - Standard (2px)</div>
                <div><code class="px-2 py-1 bg-gray-100 rounded">border-3</code> - Medium (3px)</div>
                <div><code class="px-2 py-1 bg-gray-100 rounded">border-4</code> - Thick (4px)</div>
              </div>
            </div>
          </div>

          <!-- Color Configuration -->
          <div>
            <h5 class="mb-2 text-sm font-medium text-gray-700">Color Classes</h5>
            <div class="grid grid-cols-3 gap-4 text-xs text-gray-600">
              <div>
                <strong>Dark Theme:</strong>
                <div class="mt-1 space-y-1">
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-gray-900</code></div>
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-gray-800</code></div>
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-gray-700</code></div>
                </div>
              </div>
              <div>
                <strong>Green Theme:</strong>
                <div class="mt-1 space-y-1">
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-green-600</code></div>
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-green-500</code></div>
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-emerald-600</code></div>
                </div>
              </div>
              <div>
                <strong>Light Theme:</strong>
                <div class="mt-1 space-y-1">
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-white</code></div>
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-gray-100</code></div>
                  <div><code class="px-2 py-1 bg-gray-100 rounded">border-t-gray-50</code></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Base Template -->
          <div>
            <h5 class="mb-2 text-sm font-medium text-gray-700">Base Template</h5>
            <div class="p-3 font-mono text-xs text-gray-700 rounded bg-gray-50">
              &lt;div class="w-[SIZE] h-[SIZE] border-[WIDTH] border-gray-300 border-t-[COLOR] rounded-full
              animate-spin"&gt;&lt;/div&gt;
            </div>
            <p class="mt-2 text-xs text-gray-500">Replace [SIZE], [WIDTH], and [COLOR] with your desired values</p>
          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- ========================================= -->
  <!-- SDGA PAGINATION COMPONENT EXAMPLES -->
  <!-- ========================================= -->

  <div class="mt-12 space-y-8">
    <h2 class="mb-6 font-bold text-gray-900 text-display-lg">SDGA Pagination Components</h2>
  
    <!-- Pagination Examples Container -->
    <div class="p-8 space-y-8 bg-gray-100 rounded-lg">
  
      <!-- Basic Pagination Examples -->
      <div class="space-y-6">
        <h3 class="mb-4 text-lg font-semibold text-gray-800">Basic Pagination</h3>
  
        <div class="space-y-6">
          <!-- Small Dataset (Few Pages) -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Small Dataset (4 pages) - Shows all pages</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="40"
                [itemsPerPage]="10"
                [currentPage]="2"
                [maxVisiblePages]="3"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
  
          <!-- Medium Dataset -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Medium Dataset (Current page 5 of 20)</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="200"
                [itemsPerPage]="10"
                [currentPage]="5"
                [maxVisiblePages]="3"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
  
          <!-- Large Dataset -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Large Dataset (Current page 50 of 999)</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="9990"
                [itemsPerPage]="10"
                [currentPage]="50"
                [maxVisiblePages]="3"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Size Variations -->
      <div class="space-y-6">
        <h3 class="mb-4 text-lg font-semibold text-gray-800">Size Variations</h3>
  
        <div class="space-y-6">
          <!-- Small Size -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Small Size</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="100"
                [itemsPerPage]="10"
                [currentPage]="3"
                [maxVisiblePages]="3"
                size="small"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
  
          <!-- Medium Size (Default) -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Medium Size (Default)</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="100"
                [itemsPerPage]="10"
                [currentPage]="3"
                [maxVisiblePages]="3"
                size="medium"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
  
          <!-- Large Size -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Large Size</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="100"
                [itemsPerPage]="10"
                [currentPage]="3"
                [maxVisiblePages]="3"
                size="large"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Configuration Options -->
      <div class="space-y-6">
        <h3 class="mb-4 text-lg font-semibold text-gray-800">Configuration Options</h3>
  
        <div class="space-y-6">
          <!-- Different Items Per Page -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Different Items Per Page (25 items per page)</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="500"
                [itemsPerPage]="25"
                [currentPage]="3"
                [maxVisiblePages]="7"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
  
          <!-- More Visible Pages -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">More Visible Pages (7 visible pages)</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="1000"
                [itemsPerPage]="10"
                [currentPage]="15"
                [maxVisiblePages]="7"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
  
          <!-- Disabled State -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Disabled State</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <sdga-pagination
                [totalItems]="100"
                [itemsPerPage]="10"
                [currentPage]="3"
                [maxVisiblePages]="3"
                [disabled]="true"
                (pageChange)="onPageChange($event)">
              </sdga-pagination>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Usage Examples -->
      <div class="space-y-6">
        <h3 class="mb-4 text-lg font-semibold text-gray-800">Usage Examples</h3>
  
        <div class="space-y-6">
          <!-- Table Pagination -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Table Pagination</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <div class="space-y-4">
                <!-- Mock Table -->
                <div class="overflow-hidden border border-gray-200 rounded-lg">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Name</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Email</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Role</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr *ngFor="let i of [1,2,3,4,5]">
                        <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">User {{i}}</td>
                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">user{{i}}example.com</td>
                        <td class="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">Admin</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
  
                <!-- Pagination -->
                <div class="flex items-center justify-between">
                  <div class="text-sm text-gray-700">
                    Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">97</span> results
                  </div>
                  <sdga-pagination
                    [totalItems]="97"
                    [itemsPerPage]="5"
                    [currentPage]="1"
                    [maxVisiblePages]="3"
                    size="small"
                    (pageChange)="onPageChange($event)">
                  </sdga-pagination>
                </div>
              </div>
            </div>
          </div>
  
          <!-- Card Grid Pagination -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">Card Grid Pagination</h4>
            <div class="p-4 bg-white border border-gray-200 rounded-lg">
              <div class="space-y-4">
                <!-- Mock Card Grid -->
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <div *ngFor="let i of [1,2,3,4,5,6]" class="p-4 border border-gray-200 rounded-lg">
                    <h5 class="font-medium text-gray-900">Card {{i}}</h5>
                    <p class="mt-1 text-sm text-gray-500">Sample card content for item {{i}}</p>
                  </div>
                </div>
  
                <!-- Pagination -->
                <div class="flex justify-center">
                  <sdga-pagination
                    [totalItems]="150"
                    [itemsPerPage]="6"
                    [currentPage]="2"
                    [maxVisiblePages]="3"
                    (pageChange)="onPageChange($event)">
                  </sdga-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Configuration Guide -->
      <div class="space-y-6">
        <h3 class="mb-4 text-lg font-semibold text-gray-800">Configuration Guide</h3>
  
        <div class="p-6 bg-white border border-gray-200 rounded-lg">
          <h4 class="mb-4 font-medium text-gray-800 text-md">Component Properties</h4>
  
          <div class="space-y-4">
            <!-- Input Properties -->
            <div>
              <h5 class="mb-3 text-sm font-medium text-gray-700">Input Properties</h5>
              <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                  <thead>
                    <tr class="border-b border-gray-200">
                      <th class="px-3 py-2 font-medium text-left text-gray-700">Property</th>
                      <th class="px-3 py-2 font-medium text-left text-gray-700">Type</th>
                      <th class="px-3 py-2 font-medium text-left text-gray-700">Default</th>
                      <th class="px-3 py-2 font-medium text-left text-gray-700">Description</th>
                    </tr>
                  </thead>
                  <tbody class="text-gray-600">
                    <tr class="border-b border-gray-100">
                      <td class="px-3 py-2"><code class="px-1 bg-gray-100 rounded">totalItems</code></td>
                      <td class="px-3 py-2">number</td>
                      <td class="px-3 py-2">0</td>
                      <td class="px-3 py-2">Total number of items to paginate</td>
                    </tr>
                    <tr class="border-b border-gray-100">
                      <td class="px-3 py-2"><code class="px-1 bg-gray-100 rounded">itemsPerPage</code></td>
                      <td class="px-3 py-2">number</td>
                      <td class="px-3 py-2">10</td>
                      <td class="px-3 py-2">Number of items per page</td>
                    </tr>
                    <tr class="border-b border-gray-100">
                      <td class="px-3 py-2"><code class="px-1 bg-gray-100 rounded">currentPage</code></td>
                      <td class="px-3 py-2">number</td>
                      <td class="px-3 py-2">1</td>
                      <td class="px-3 py-2">Current active page</td>
                    </tr>
                    <tr class="border-b border-gray-100">
                      <td class="px-3 py-2"><code class="px-1 bg-gray-100 rounded">maxVisiblePages</code></td>
                      <td class="px-3 py-2">number</td>
                      <td class="px-3 py-2">5</td>
                      <td class="px-3 py-2">Maximum number of page buttons to show</td>
                    </tr>
                    <tr class="border-b border-gray-100">
                      <td class="px-3 py-2"><code class="px-1 bg-gray-100 rounded">size</code></td>
                      <td class="px-3 py-2">'small' | 'medium' | 'large'</td>
                      <td class="px-3 py-2">'medium'</td>
                      <td class="px-3 py-2">Size of pagination buttons</td>
                    </tr>
                    <tr class="border-b border-gray-100">
                      <td class="px-3 py-2"><code class="px-1 bg-gray-100 rounded">disabled</code></td>
                      <td class="px-3 py-2">boolean</td>
                      <td class="px-3 py-2">false</td>
                      <td class="px-3 py-2">Disable all pagination controls</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
  
            <!-- Output Events -->
            <div>
              <h5 class="mb-3 text-sm font-medium text-gray-700">Output Events</h5>
              <div class="p-3 rounded bg-gray-50">
                <div class="space-y-2">
                  <div>
                    <code class="px-2 py-1 text-sm bg-gray-100 rounded">pageChange</code>
                    <span class="ml-2 text-sm text-gray-600">Emitted when page changes</span>
                  </div>
                  <div class="ml-4 text-xs text-gray-500">
                    Event payload: <code>{{ '{' }} page: number, itemsPerPage: number, totalItems: number, totalPages: number {{ '}' }}</code>
                  </div>
                </div>
              </div>
            </div>
  
            <!-- Usage Example -->
            <div>
              <h5 class="mb-3 text-sm font-medium text-gray-700">Basic Usage</h5>
              <div class="p-3 overflow-x-auto font-mono text-xs text-gray-700 rounded bg-gray-50">
                <div>&lt;sdga-pagination</div>
                <div class="ml-2">[totalItems]="totalItems"</div>
                <div class="ml-2">[itemsPerPage]="itemsPerPage"</div>
                <div class="ml-2">[currentPage]="currentPage"</div>
                <div class="ml-2">[maxVisiblePages]="3"</div>
                <div class="ml-2">size="medium"</div>
                <div class="ml-2">(pageChange)="onPageChange($event)"&gt;</div>
                <div>&lt;/sdga-pagination&gt;</div>
              </div>
            </div>
  
            <!-- TypeScript Integration -->
            <div>
              <h5 class="mb-3 text-sm font-medium text-gray-700">TypeScript Integration</h5>
              <div class="p-3 overflow-x-auto font-mono text-xs text-gray-700 rounded bg-gray-50">
                <div>import {{ '{' }} PageChangeEvent {{ '}' }} from '&#64;shared/components/pagination/pagination';</div>
                <div class="mt-2">onPageChange(event: PageChangeEvent) {{ '{' }}</div>
                <div class="ml-2">this.currentPage = event.page;</div>
                <div class="ml-2">// Load data for new page</div>
                <div>{{ '}' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
  
    </div>
  </div>
  <div class="mt-12 space-y-8">
    <h2 class="mb-6 font-bold text-gray-900 text-display-lg">SDGA Chips</h2>
    <small>Feel free to use the html element that fits your needs. (ex: button, span, etc.)</small>
    <div class="flex items-center p-5 justify-evenly">
      <div class="flex gap-8">
        <div class="flex flex-col items-start gap-4">
          <span tabindex="0" class="rounded-full chip chip-lg chip-primary">Item</span> 
          <span tabindex="0" class="rounded-full chip chip-md chip-primary">Item</span>
          <span tabindex="0" class="rounded-full chip chip-sm chip-primary">Item</span>
        </div>
        <div class="flex flex-col items-start gap-4">
          <span tabindex="0" class="chip chip-lg chip-primary">Item</span>
          <span tabindex="0" class="chip chip-md chip-primary">Item</span>
          <span tabindex="0" class="chip chip-sm chip-primary">Item</span>
        </div>
        <div class="flex flex-col items-start gap-4">
          <button tabindex="0" class="rounded-full chip chip-lg chip-primary" disabled>Item</button>
          <button tabindex="0" class="rounded-full chip chip-md chip-primary" disabled>Item</button>
          <button tabindex="0" class="rounded-full chip chip-sm chip-primary" disabled>Item</button>
        </div>
        <div class="flex flex-col items-start gap-4">
          <button tabindex="0" class="chip chip-lg chip-primary" disabled>Item</button>
          <button tabindex="0" class="chip chip-md chip-primary" disabled>Item</button>
          <button tabindex="0" class="chip chip-sm chip-primary" disabled>Item</button>
        </div>
      </div>
      <!-- ------------------------------------------------- -->
       <div class="flex gap-8">
         <div class="flex flex-col items-start gap-4">
            <span tabindex="0" class="rounded-full chip chip-lg chip-neutral">Item</span> 
            <span tabindex="0" class="rounded-full chip chip-md chip-neutral">Item</span>
            <span tabindex="0" class="rounded-full chip chip-sm chip-neutral">Item</span>
          </div>
          <div class="flex flex-col items-start gap-4">
            <span tabindex="0" class="chip chip-lg chip-neutral">Item</span>
            <span tabindex="0" class="chip chip-md chip-neutral">Item</span>
            <span tabindex="0" class="chip chip-sm chip-neutral">Item</span>
          </div>
          <div class="flex flex-col items-start gap-4">
            <button tabindex="0" class="rounded-full chip chip-lg chip-neutral" disabled>Item</button>
            <button tabindex="0" class="rounded-full chip chip-md chip-neutral" disabled>Item</button>
            <button tabindex="0" class="rounded-full chip chip-sm chip-neutral" disabled>Item</button>
          </div>
          <div class="flex flex-col items-start gap-4">
            <button tabindex="0" class="chip chip-lg chip-neutral" disabled>Item</button>
            <button tabindex="0" class="chip chip-md chip-neutral" disabled>Item</button>
            <button tabindex="0" class="chip chip-sm chip-neutral" disabled>Item</button>
          </div>
       </div>
    </div>
    <div class="flex items-center justify-center p-5 bg-sa-800">
      <div class="flex gap-8">
        <div class="flex flex-col items-start gap-4">
          <span tabindex="0" class="rounded-full chip chip-lg chip-oncolor">Item</span> 
          <span tabindex="0" class="rounded-full chip chip-md chip-oncolor">Item</span>
          <span tabindex="0" class="rounded-full chip chip-sm chip-oncolor">Item</span>
        </div>
        <div class="flex flex-col items-start gap-4">
          <span tabindex="0" class="chip chip-lg chip-oncolor">Item</span>
          <span tabindex="0" class="chip chip-md chip-oncolor">Item</span>
          <span tabindex="0" class="chip chip-sm chip-oncolor">Item</span>
        </div>
        <div class="flex flex-col items-start gap-4">
          <button tabindex="0" class="rounded-full chip chip-lg chip-oncolor" disabled>Item</button>
          <button tabindex="0" class="rounded-full chip chip-md chip-oncolor" disabled>Item</button>
          <button tabindex="0" class="rounded-full chip chip-sm chip-oncolor" disabled>Item</button>
        </div>
        <div class="flex flex-col items-start gap-4">
          <button tabindex="0" class="chip chip-lg chip-oncolor" disabled>Item</button>
          <button tabindex="0" class="chip chip-md chip-oncolor" disabled>Item</button>
          <button tabindex="0" class="chip chip-sm chip-oncolor" disabled>Item</button>
        </div>
      </div>
    </div>
  </div>
</div>
