<div class="grid grid-cols-1 gap-8 p-4 md:grid-cols-2">
  <!-- Linear Example -->
  <div>
    <sdga-progress-bar
      type="linear"
      label="Label"
      helpText="sucess"
      [value]="60"
      status="success"
      [barHeightClass]="'h-2'"
       status="success"
      [showStatusIcon]="true"
    ></sdga-progress-bar>

        <sdga-progress-bar
      type="linear"
      label="Label"
      helpText="Error"
      [value]="90"
      status="error"
      [barHeightClass]="'h-1'"
      [showPercentage]="true"
    ></sdga-progress-bar>

        <sdga-progress-bar
      type="linear"
      label="Label"
      helpText="Help text"
      [value]="20"
      status="neutral"
      [barHeightClass]="'h-4'"
      [showPercentage]="true"
    ></sdga-progress-bar>
  </div>

  <!-- Circular Example -->
  <div class="grid grid-cols-1 gap-8 p-4 md:grid-cols-3">
        <sdga-progress-bar
      type="circular"
      [value]="60"
      label="Active users"
      size="lg"
      status="neutral"
      [showStatusIcon]="false"
    ></sdga-progress-bar>
    <sdga-progress-bar
      type="circular"
      [value]="80"
      label="Active users"
      size="md"
      status="success"
      [showStatusIcon]="true"
    ></sdga-progress-bar>
    <sdga-progress-bar
      type="circular"
      [value]="45"
      label="Errors"
      size="sm"
      status="error"
      [showStatusIcon]="true"
    ></sdga-progress-bar>

  </div>
</div>
