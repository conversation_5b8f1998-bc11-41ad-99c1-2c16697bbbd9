import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class PreviewService {
  private previewSubject = new BehaviorSubject<boolean>(true);
  preview$ = this.previewSubject.asObservable();

  setPreview(show: boolean) {
    this.previewSubject.next(show);
  }

  togglePreview() {
    this.previewSubject.next(!this.previewSubject.value);
  }
}
