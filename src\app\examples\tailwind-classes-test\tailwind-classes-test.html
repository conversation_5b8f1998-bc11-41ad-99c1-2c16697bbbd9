<!-- Tailwind usage -->
<div class="p-4 rounded-lg bg-sa-500 text-gray-25">
  Saudi Government Primary Section
</div>

<div class="p-4 rounded bg-lavender-100 text-lavender-800">
  Welcome to the calm zone ✨
</div>

<div class="p-4 border-l-4 rounded bg-error-50 text-error-800 border-error-500">
  <strong>Error:</strong> Please check the form for validation issues.
</div>

<div class="p-4 border-l-4 rounded bg-warning-50 text-warning-800 border-warning-500">
  ⚠️ Please double-check the information you entered.
</div>

<div class="p-4 border-l-4 rounded bg-info-50 text-info-800 border-info-500">
  💡 Your report is being generated. Please wait a few moments.
</div>

<div class="p-4 border-l-4 rounded bg-success-50 text-success-800 border-success-500">
  ✅ Operation completed successfully.
</div>

<div class="p-4 text-white rounded bg-gradient-sa-01">
  Beautiful Saudi Green Gradient!
</div>

<!-- Light background -->
<p class="text-primary-light">Primary text on light</p>
<p class="text-secondary-light">Secondary text on light</p>

<!-- Dark background -->
<div class="p-4 bg-gray-900">
  <p class="text-primary-dark">Primary text on dark</p>
  <p class="text-secondary-dark">Secondary text on dark</p>
</div>

<!-- Platforms branding -->
<p class="text-sa-primary">SA Primary</p>

<!-- Semantic usage -->
<p class="text-error-primary">This is an error message.</p>
<h1 class="font-bold font-arabic text-display-md md:text-display-xl lg:text-display-2xl">عنوان متجاوب</h1>


<div class="shadow-sa-3xl backdrop-blur-sa-xl">
  ...
</div>

<div class="mx-auto mt-8 bg-gray-100 p-container-desktop max-w-container-desktop">
  <h1 class="text-2xl font-bold mb-xl">Tailwind Spacing & Width Test</h1>

  <div class="bg-white rounded-lg shadow-md p-xl">
    <p class="text-gray-700 mb-md max-w-paragraph">
      This paragraph uses <strong>max-w-paragraph</strong> and <strong>mb-md</strong> to test custom Tailwind spacing
      and widths. You should see consistent spacing and readable line length if everything is configured correctly.
    </p>

    <div class="flex flex-wrap gap-3xl">
      <div class="flex items-center justify-center w-40 h-20 text-white bg-blue-500 rounded-md">
        Box 1
      </div>
      <div class="flex items-center justify-center w-40 h-20 text-white bg-green-500 rounded-md">
        Box 2
      </div>
      <div class="flex items-center justify-center w-40 h-20 text-white bg-purple-500 rounded-md">
        Box 3
      </div>
    </div>
  </div>
</div>

<div class="max-w-xl mx-auto p-xl space-y-lg">
  <div class="bg-sa-200 p-md radius-none">radius-none</div>
  <div class="bg-sa-300 p-md rounded-xs">radius-xs</div>
  <div class="rounded-sm bg-sa-400 p-md">radius-sm</div>
  <div class="text-white rounded-md bg-sa-500 p-md">radius-md</div>
  <div class="text-white rounded-lg bg-sa-600 p-md">radius-lg</div>
  <div class="text-white bg-sa-700 p-md rounded-xl">radius-xl</div>
  <div class="text-white rounded-full bg-sa-800 p-md">radius-full</div>
</div>


<!-- 12-column layout -->
<div class="grid grid-cols-12 gap-gutter-desktop">
  <div class="col-span-4">Left</div>
  <div class="col-span-8">Right</div>
</div>

<!-- 3-column layout -->
<div class="grid grid-cols-3 gap-gutter-tablet">
  <div>Col 1</div>
  <div>Col 2</div>
  <div>Col 3</div>
</div>


<section class="container py-10 mx-auto bg-gray-50">
  <div class="grid grid-cols-12 mx-auto gap-gutter-desktop max-w-container-desktop">
    <div class="col-span-3 p-4 bg-blue-100">Sidebar</div>
    <div class="col-span-9 p-4 bg-blue-200">Main content</div>
  </div>
</section>




<div class="container">
  <div class="container">
    <h1 class="my-lg">Test Shared Tailwind classes</h1>

    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
      <div class="card">
        hello
      </div>
      <div class="card card-disabled">
        hello
      </div>
      <div class="card card-border-black">
        hello
      </div>
    </div>
  </div>

  <sdga-navbar></sdga-navbar>