<div class="rounded-xl w-full max-w-xl mx-auto">
  <ol class="relative border-l-2 border-gray-300 m-6">
    <li *ngFor="let step of steps; let i = index" class="mb-10 ml-6">
       <!-- Step number circle -->
    <span
      class="absolute  flex items-center justify-center w-8 h-8 text-xs font-medium border rounded-full -left-4 "
      [ngClass]="{
        'bg-sa-600 text-white border-sa-600 border-2': i < currentStep,
        'border-sa-600 text-sa-600 !bg-white border-2': i === currentStep,
        'border-gray-300 text-gray-300 bg-white': i > currentStep
      }">
      <ng-container *ngIf="i < currentStep; else stepNumber">
<sdga-svg-icon name="correct" svgClass="text-white"></sdga-svg-icon>

  </ng-container>
  <ng-template #stepNumber>
    {{ i + 1 }}
  </ng-template>
    </span>

    <h3
      class="font-medium leading-tight"
      [ngClass]="{
        'text-sa-600': i === currentStep,
        'text-gray-900': i < currentStep,
        'text-gray-300': i > currentStep
      }">
      {{ step.title }}
    </h3>
      <ng-container *ngIf="i === currentStep">
        <ng-container *ngTemplateOutlet="step.content"></ng-container>
      </ng-container>
    </li>
  </ol>

  <!-- Navigation Buttons -->
  <div class="flex justify-start space-x-6">
    <button
      class="px-4 py-2 rounded bg-white border-gray-300 border-2 text-black hover:bg-gray-300 disabled:opacity-50"
      (click)="back()" [disabled]="currentStep === 0">
            {{ 'GENERAL.BACK' | translate }}

    </button>

    <button
      class="px-4 py-2 rounded bg-green-600 text-white hover:bg-green-700 disabled:opacity-50"
      (click)="next()" [disabled]="currentStep === steps.length - 1">
      {{ 'GENERAL.NEXT' | translate }}
    </button>
  </div>
</div>
