import { Component, Input } from '@angular/core';
import { SvgIcon } from "../svg-icon/svg-icon";
import { CommonModule } from '@angular/common';
type ProgressStatus = 'success' | 'error' | 'neutral';
type ProgressType = 'linear' | 'circular';
type ProgressSize = 'sm' | 'md' | 'lg';
@Component({
  selector: 'sdga-progress-bar',
  imports: [SvgIcon,CommonModule],
  templateUrl: './progress-bar.html',
  styleUrl: './progress-bar.scss'
})
export class ProgressBar {
@Input() type: ProgressType = 'linear';
  @Input() size: ProgressSize = 'md'; // only for circular
  @Input() value: number = 0;
  @Input() label?: string;
  @Input() helpText?: string;
  @Input() showPercentage: boolean = false;
  @Input() showStatusIcon: boolean = false;
  @Input() status: ProgressStatus = 'neutral';
  @Input() barHeightClass: string | undefined;

  readonly radius = 16;
  readonly strokeWidth = 3;
  circumference = 2 * Math.PI * this.radius;


  ngOnChanges() {
    this.value = Math.max(0, Math.min(100, this.value)); // Clamp 0–100
  }

  get statusIconClass(): string {
    if (this.status === 'success') return 'text-success-700';
    if (this.status === 'error') return 'text-error-700';
    if (this.status === 'neutral') return 'text-gray-700';
    return '';
  }

}
