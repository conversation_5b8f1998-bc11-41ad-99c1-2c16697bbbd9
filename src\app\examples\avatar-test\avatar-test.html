<div class="max-w-6xl min-h-screen p-6 mx-auto bg-gray-50 shadow-sa-md">
  <h1 class="mb-6 font-bold text-gray-900 text-display-md">Avatar Component Test</h1>

  <!-- Image Paths Information -->
  <section class="p-4 mb-6 border border-blue-200 rounded-lg bg-blue-50">
    <h2 class="mb-2 text-lg font-semibold text-blue-800">📁 Image Files Used in Testing</h2>
    <div class="space-y-1 text-sm text-blue-700">
      <p><strong>Valid Image:</strong> {{ personImagePath }}</p>
      <p><strong>Logo Image:</strong> {{ logoImagePath }}</p>
      <p><strong>Invalid Image (for fallback testing):</strong> {{ invalidImagePath }}</p>
    </div>
    <p class="mt-2 text-xs text-blue-600">
      The valid images exist in the project's assets folder and should load successfully.
      The invalid image path is used to test fallback behavior.
    </p>
  </section>

  <div class="space-y-8">

    <!-- <PERSON><PERSON> Sizes -->
    <section class="p-6 bg-white rounded-lg shadow-sm">
      <h2 class="mb-4 text-2xl font-semibold text-gray-800">Avatar Sizes</h2>
      <div class="space-y-4">

        <!-- Circular Avatars with Initials -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Circular - Initials</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar size="xs" initials="AB" shape="circle"></sdga-avatar>
            <sdga-avatar size="sm" initials="AB" shape="circle"></sdga-avatar>
            <sdga-avatar size="md" initials="AB" shape="circle"></sdga-avatar>
            <sdga-avatar size="lg" initials="AB" shape="circle"></sdga-avatar>
            <sdga-avatar size="xl" initials="AB" shape="circle"></sdga-avatar>
            <sdga-avatar size="2xl" initials="AB" shape="circle"></sdga-avatar>
            <sdga-avatar size="3xl" initials="AB" shape="circle"></sdga-avatar>
          </div>
        </div>

        <!-- Square Avatars with Initials -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Square - Initials</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar size="xs" initials="AB" shape="square"></sdga-avatar>
            <sdga-avatar size="sm" initials="AB" shape="square"></sdga-avatar>
            <sdga-avatar size="md" initials="AB" shape="square"></sdga-avatar>
            <sdga-avatar size="lg" initials="AB" shape="square"></sdga-avatar>
            <sdga-avatar size="xl" initials="AB" shape="square"></sdga-avatar>
            <sdga-avatar size="2xl" initials="AB" shape="square"></sdga-avatar>
            <sdga-avatar size="3xl" initials="AB" shape="square"></sdga-avatar>
          </div>
        </div>

        <!-- Circular Avatars with Icons -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Circular - Icons</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar size="xs" icon="user" shape="circle"></sdga-avatar>
            <sdga-avatar size="sm" icon="user" shape="circle"></sdga-avatar>
            <sdga-avatar size="md" icon="user" shape="circle"></sdga-avatar>
            <sdga-avatar size="lg" icon="user" shape="circle"></sdga-avatar>
            <sdga-avatar size="xl" icon="user" shape="circle"></sdga-avatar>
            <sdga-avatar size="2xl" icon="user" shape="circle"></sdga-avatar>
            <sdga-avatar size="3xl" icon="user" shape="circle"></sdga-avatar>
          </div>
        </div>

        <!-- Square Avatars with Icons -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Square - Icons</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar size="xs" icon="user" shape="square"></sdga-avatar>
            <sdga-avatar size="sm" icon="user" shape="square"></sdga-avatar>
            <sdga-avatar size="md" icon="user" shape="square"></sdga-avatar>
            <sdga-avatar size="lg" icon="user" shape="square"></sdga-avatar>
            <sdga-avatar size="xl" icon="user" shape="square"></sdga-avatar>
            <sdga-avatar size="2xl" icon="user" shape="square"></sdga-avatar>
            <sdga-avatar size="3xl" icon="user" shape="square"></sdga-avatar>
          </div>
        </div>

        <!-- Circular Avatars with Images -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Circular - Real Images</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar size="xs" [src]="personImagePath" alt="Person Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="sm" [src]="personImagePath" alt="Person Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="md" [src]="personImagePath" alt="Person Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="lg" [src]="personImagePath" alt="Person Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="xl" [src]="personImagePath" alt="Person Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="2xl" [src]="personImagePath" alt="Person Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="3xl" [src]="personImagePath" alt="Person Avatar" shape="circle"></sdga-avatar>
          </div>
        </div>

        <!-- Square Avatars with Images -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Square - Real Images</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar size="xs" [src]="personImagePath" alt="Person Avatar" shape="square"></sdga-avatar>
            <sdga-avatar size="sm" [src]="personImagePath" alt="Person Avatar" shape="square"></sdga-avatar>
            <sdga-avatar size="md" [src]="personImagePath" alt="Person Avatar" shape="square"></sdga-avatar>
            <sdga-avatar size="lg" [src]="personImagePath" alt="Person Avatar" shape="square"></sdga-avatar>
            <sdga-avatar size="xl" [src]="personImagePath" alt="Person Avatar" shape="square"></sdga-avatar>
            <sdga-avatar size="2xl" [src]="personImagePath" alt="Person Avatar" shape="square"></sdga-avatar>
            <sdga-avatar size="3xl" [src]="personImagePath" alt="Person Avatar" shape="square"></sdga-avatar>
          </div>
        </div>

        <!-- Logo Images for Variety -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Logo Images</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar size="md" [src]="logoImagePath" alt="Logo Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="lg" [src]="logoImagePath" alt="Logo Avatar" shape="circle"></sdga-avatar>
            <sdga-avatar size="xl" [src]="logoImagePath" alt="Logo Avatar" shape="square"></sdga-avatar>
            <sdga-avatar size="2xl" [src]="logoImagePath" alt="Logo Avatar" shape="square"></sdga-avatar>
          </div>
        </div>
      </div>
    </section>

    <!-- Custom Background Colors -->
    <section class="p-6 bg-white rounded-lg shadow-sm">
      <h2 class="mb-4 text-2xl font-semibold text-gray-800">Custom Background Colors</h2>
      <div class="flex items-center gap-4">
        <sdga-avatar initials="AB" backgroundColor="bg-red-500"></sdga-avatar>
        <sdga-avatar initials="CD" backgroundColor="bg-blue-500"></sdga-avatar>
        <sdga-avatar initials="EF" backgroundColor="bg-green-500"></sdga-avatar>
        <sdga-avatar initials="GH" backgroundColor="bg-purple-500"></sdga-avatar>
        <sdga-avatar initials="IJ" backgroundColor="bg-yellow-500"></sdga-avatar>
        <sdga-avatar initials="KL" backgroundColor="bg-pink-500"></sdga-avatar>
      </div>
    </section>

    <!-- Avatar from Name -->
    <section class="p-6 bg-white rounded-lg shadow-sm">
      <h2 class="mb-4 text-2xl font-semibold text-gray-800">Avatar from Name (Auto-generated Initials)</h2>
      <div class="flex items-center gap-4">
        <sdga-avatar name="John Doe"></sdga-avatar>
        <sdga-avatar name="Jane Smith"></sdga-avatar>
        <sdga-avatar name="Michael Johnson"></sdga-avatar>
        <sdga-avatar name="Sarah Wilson"></sdga-avatar>
        <sdga-avatar name="David Brown"></sdga-avatar>
      </div>
    </section>

    <!-- Avatar Groups -->
    <section class="p-6 bg-white rounded-lg shadow-sm">
      <h2 class="mb-4 text-2xl font-semibold text-gray-800">Avatar Groups</h2>

      <div class="space-y-6">
        <!-- Small Group -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Small Group (3 avatars)</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 3)" size="md"></sdga-avatar>
        </div>

        <!-- Medium Group with Overflow -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Medium Group (4 avatars, max 3 visible)</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 4)" [maxVisible]="3" size="md"></sdga-avatar>
        </div>

        <!-- Large Group with Overflow -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Large Group (6 avatars, max 4 visible)</h3>
          <sdga-avatar [avatars]="sampleAvatars" [maxVisible]="4" size="md"></sdga-avatar>
        </div>

        <!-- Extra Large Group -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Extra Large Group (8 avatars, max 5 visible)</h3>
          <sdga-avatar [avatars]="largeAvatarGroup" [maxVisible]="5" size="md"></sdga-avatar>
        </div>

        <!-- Mixed Content Group -->
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Mixed Content Group (Images, Icons, Initials)</h3>
          <sdga-avatar [avatars]="mixedContentAvatars" [maxVisible]="4" size="lg"></sdga-avatar>
          <p class="mt-2 text-sm text-gray-500">
            This group demonstrates: real image, fallback to initials, icon avatar, and initials-only avatar
          </p>
        </div>
      </div>
    </section>

    <!-- Different Sizes for Groups -->
    <section class="p-6 bg-white rounded-lg shadow-sm">
      <h2 class="mb-4 text-2xl font-semibold text-gray-800">Avatar Groups - Different Sizes</h2>

      <div class="space-y-4">
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Extra Small Group</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 5)" [maxVisible]="3" size="xs"></sdga-avatar>
        </div>

        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Small Group</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 5)" [maxVisible]="3" size="sm"></sdga-avatar>
        </div>

        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Medium Group</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 5)" [maxVisible]="3" size="md"></sdga-avatar>
        </div>

        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Large Group</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 5)" [maxVisible]="3" size="lg"></sdga-avatar>
        </div>

        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Extra Large Group</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 5)" [maxVisible]="3" size="xl"></sdga-avatar>
        </div>
      </div>
    </section>

    <!-- Square Avatar Groups -->
    <section class="p-6 bg-white rounded-lg shadow-sm">
      <h2 class="mb-4 text-2xl font-semibold text-gray-800">Square Avatar Groups</h2>

      <div class="space-y-4">
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Square Medium Group</h3>
          <sdga-avatar [avatars]="sampleAvatars.slice(0, 4)" [maxVisible]="3" size="md" shape="square"></sdga-avatar>
        </div>

        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Square Large Group</h3>
          <sdga-avatar [avatars]="sampleAvatars" [maxVisible]="4" size="lg" shape="square"></sdga-avatar>
        </div>
      </div>
    </section>

    <!-- Mixed Content Types -->
    <section class="p-6 bg-white rounded-lg shadow-sm">
      <h2 class="mb-4 text-2xl font-semibold text-gray-800">Mixed Content Types</h2>

      <div class="space-y-4">
        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Icons Only</h3>
          <div class="flex items-center gap-4">
            <sdga-avatar icon="user" size="md"></sdga-avatar>
            <sdga-avatar icon="user" size="md" shape="square"></sdga-avatar>
            <sdga-avatar icon="user" size="lg"></sdga-avatar>
            <sdga-avatar icon="user" size="lg" shape="square"></sdga-avatar>
          </div>
        </div>

        <div>
          <h3 class="mb-3 text-lg font-medium text-gray-700">Image Loading & Fallback Behavior</h3>
          <div class="space-y-4">
            <!-- Successful Image Loading -->
            <div>
              <h4 class="mb-2 text-sm font-medium text-gray-600">✅ Successful Image Loading</h4>
              <div class="flex items-center gap-4">
                <sdga-avatar [src]="personImagePath" alt="Working Image" size="md"></sdga-avatar>
                <sdga-avatar [src]="logoImagePath" alt="Working Logo" size="md" shape="square"></sdga-avatar>
                <span class="text-sm text-gray-500">These avatars load real images from assets/images/</span>
              </div>
            </div>

            <!-- Failed Image Loading with Fallbacks -->
            <div>
              <h4 class="mb-2 text-sm font-medium text-gray-600">❌ Failed Image Loading (Fallback to Initials)</h4>
              <div class="flex items-center gap-4">
                <sdga-avatar [src]="invalidImagePath" initials="FB" alt="Fallback to Initials" size="md"></sdga-avatar>
                <sdga-avatar [src]="invalidImagePath" name="Fallback User" alt="Fallback to Name Initials" size="md"></sdga-avatar>
                <span class="text-sm text-gray-500">These avatars try to load invalid images and fall back to initials</span>
              </div>
            </div>

            <!-- Failed Image Loading with Icon Fallback -->
            <div>
              <h4 class="mb-2 text-sm font-medium text-gray-600">❌ Failed Image Loading (Fallback to Icon)</h4>
              <div class="flex items-center gap-4">
                <sdga-avatar [src]="invalidImagePath" icon="user" alt="Fallback to Icon" size="md"></sdga-avatar>
                <sdga-avatar [src]="invalidImagePath" icon="user" alt="Fallback to Icon Square" size="md" shape="square"></sdga-avatar>
                <span class="text-sm text-gray-500">These avatars try to load invalid images and fall back to icons</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

  </div>
</div>