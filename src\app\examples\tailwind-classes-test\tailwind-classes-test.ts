import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Navbar } from '../../shared/components/navbar/navbar';
import { Accordion } from '../../shared/components/accordion/accordion';
import { SkeletonloadingService } from '../../core/services/skeletonloading-service';
import { SvgIcon } from '../../shared/components/svg-icon/svg-icon';
import { Pagination, PageChangeEvent } from '../../shared/components/pagination/pagination';

@Component({
  selector: 'sdga-tailwind-classes-test',
  imports: [CommonModule, Navbar, SvgIcon, Pagination],
  templateUrl: './tailwind-classes-test.html',
  styleUrl: './tailwind-classes-test.scss',
})
export class TailwindClassesTest implements OnInit {
  isLoadingLocal: boolean = false;

  constructor(private skeletonLoader: SkeletonloadingService) {}

  ngOnInit(): void {
    this.skeletonLoader.loading$.subscribe((loading: boolean) => {
      this.isLoadingLocal = loading;
    });
  }

  onPageChange(event: PageChangeEvent): void {
    console.log('Page changed:', event);
    // Handle page change logic here
    // For example: load new data, update URL, etc.
  }
}
