import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormInput } from '../../shared/components/inputs/form-input/form-input';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';

@Component({
  selector: 'sdga-inputs-test',
  imports: [CommonModule, FormInput, ReactiveFormsModule],
  templateUrl: './inputs-test.html',
  styleUrl: './inputs-test.scss'
})
export class InputsTest {
  testInputs: FormGroup;
  formSubmitted = false;

  constructor(private fb: FormBuilder) {
    this.testInputs = this.fb.group({
      text: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      number: ['', [Validators.required]],
      decimal: ['', [Validators.required]],
      textarea: ['', [Validators.required, Validators.maxLength(100)]],
      checkboxSm: [false],
      checkboxMd: [true],
      checkboxLg: [false, [Validators.requiredTrue]],
      singleDate: ['', [Validators.required]],
      dateRange: [''],
      disabled: [{value: 'This is disabled', disabled: true}],
      readonly: ['This is readonly']
    });
  }

  onSubmit() {
    this.formSubmitted = true;
    if (this.testInputs.valid) {
      console.log('Form submitted:', this.testInputs.value);
    } else {
      console.log('Form has errors:', this.testInputs.errors);
    }
  }

  resetForm() {
    this.formSubmitted = false;
    this.testInputs.reset();
  }
}
