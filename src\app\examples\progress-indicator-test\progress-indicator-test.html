<sdga-progress-indicator
  [steps]="[
    { title: 'Step One', content: stepOneTpl },
    { title: 'Step Two', content: stepTwoTpl },
    { title: 'Step Three', content: stepThreeTpl }
  ]"
  [currentStep]="currentStep"
  (stepChange)="currentStep = $event">
</sdga-progress-indicator>

<ng-template #stepOneTpl>
  <p class="text-sm text-gray-500">This is a simple text description for Step One.</p>
</ng-template>

<ng-template #stepTwoTpl>
  <form class="space-y-2">
    <label class="block">
      <span class="text-sm font-medium text-gray-700">Email</span>
      <input type="email" class="mt-1 block w-full border px-3 py-2 rounded" />
    </label>
  </form>
</ng-template>

<ng-template #stepThreeTpl>
  <table class="w-full text-sm text-left text-gray-500 border mt-2">
    <thead>
      <tr class="bg-gray-100 text-xs text-gray-700">
        <th class="px-3 py-2">Name</th>
        <th class="px-3 py-2">Value</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="px-3 py-2">Item A</td>
        <td class="px-3 py-2">123</td>
      </tr>
    </tbody>
  </table>
</ng-template>
