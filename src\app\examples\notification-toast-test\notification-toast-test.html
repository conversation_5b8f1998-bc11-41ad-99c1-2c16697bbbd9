<div class="grid grid-cols-4 gap-4 p-6 bg-gray-200">
  <div class="col-span-2">
    <sdga-notifications
      [notificationType]="'error'"
      [title]="'Information'"
      [content]="'This is a dynamic message.'"
      [isVertical]="false"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"
      [buttons]="[
    { label: 'Confirm', onClick: onConfirm },
    { label: 'Cancel', onClick: onCancel }
  ]"
    >
    </sdga-notifications>
  </div>
  <div class="col-span-2">
    <sdga-notifications
      [notificationType]="'success'"
      [title]="'Information'"
      [content]="'This is a dynamic message.'"
      [isVertical]="false"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"
      [buttons]="[
    { label: 'Confirm', onClick: onConfirm },
    { label: 'Cancel', onClick: onCancel }
  ]"
    >
    </sdga-notifications>
  </div>

   <div class="col-span-2">
    <sdga-notifications
      [notificationType]="'warning'"
      [title]="'Information'"
      [content]="'This is a dynamic message.'"
      [isVertical]="true"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"
      [buttons]="[
    { label: 'Confirm', onClick: onConfirm },
    { label: 'Cancel', onClick: onCancel }
  ]"
    >
    </sdga-notifications>
  </div>

   <div class="col-span-2">
    <sdga-notifications
      [notificationType]="'info'"
      [title]="'Information'"
      [content]="'This is a dynamic message.'"
      [isVertical]="true"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"
      [buttons]="[
    { label: 'Confirm', onClick: onConfirm },
    { label: 'Cancel', onClick: onCancel }
  ]"
    >
    </sdga-notifications>
  </div>

</div>
