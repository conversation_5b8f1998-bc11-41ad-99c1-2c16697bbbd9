<div class="max-w-4xl min-h-screen p-6 mx-auto bg-gray-50">
  <div class="p-6 bg-white rounded-lg shadow-sa-md">
    <h1 class="mb-6 font-bold text-gray-900 text-display-md" translate="TEST_PAGE.TITLE"></h1>

    <!-- Language Demo Section -->
    <!-- <div class="p-4 mb-6 border border-blue-200 rounded-lg bg-blue-50">
      <h2 class="mb-2 text-lg font-semibold text-blue-900" translate="TEST_PAGE.LANGUAGE_DEMO_TITLE"></h2>
      <p class="mb-2 text-sm text-blue-700" translate="TEST_PAGE.LANGUAGE_DEMO_DESC"></p>
      <div class="flex items-center space-x-2 text-xs text-blue-600">
        <span translate="TEST_PAGE.CURRENT_LANGUAGE"></span>
        <span class="font-medium">{{ getCurrentLanguage() }}</span>
      </div>
    </div> -->

    <form [formGroup]="testInputs" (ngSubmit)="onSubmit()" class="space-y-6">

      <!-- Text Inputs Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.TEXT_INPUTS"></h2>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <sdga-form-input
            name="text"
            type="text"
            [label]="'FORM_LABELS.TEXT_INPUT' | translate"
            [control]="testInputs.get('text')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            [placeholder]="'PLACEHOLDERS.ENTER_TEXT' | translate"
          ></sdga-form-input>

          <sdga-form-input
            name="email"
            type="email"
            [label]="'FORM_LABELS.EMAIL' | translate"
            [control]="testInputs.get('email')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            [placeholder]="'PLACEHOLDERS.ENTER_EMAIL' | translate"
          ></sdga-form-input>

          <sdga-form-input
            name="password"
            type="password"
            [label]="'FORM_LABELS.PASSWORD' | translate"
            [control]="testInputs.get('password')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            [placeholder]="'PLACEHOLDERS.ENTER_PASSWORD' | translate"
          ></sdga-form-input>

          <sdga-form-input
            name="singleDate"
            type="date"
            [label]="'FORM_LABELS.DATE' | translate"
            [control]="testInputs.get('singleDate')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Number Inputs Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.NUMBER_INPUTS"></h2>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <sdga-form-input
            name="number"
            type="number"
            [label]="'FORM_LABELS.INTEGER_NUMBER' | translate"
            [control]="testInputs.get('number')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            [placeholder]="'PLACEHOLDERS.ENTER_NUMBER' | translate"
            min="1"
            max="100"
          ></sdga-form-input>

          <sdga-form-input
            name="decimal"
            type="number"
            [label]="'FORM_LABELS.DECIMAL_NUMBER' | translate"
            [control]="testInputs.get('decimal')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            [placeholder]="'PLACEHOLDERS.ENTER_DECIMAL' | translate"
            step="any"
            min="0"
            max="999.99"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Radio Button Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.RADIO_BUTTONS"></h2>

        <div class="space-y-3">
          <sdga-form-input
            name="radioGroup"
            type="radio"
            [label]="'FORM_LABELS.RADIO_OPTION_1' | translate"
            [description]="'DESCRIPTIONS.RADIO_OPTION_1' | translate"
            [control]="testInputs.get('radioGroup')!"
            [formSubmitted]="formSubmitted"
            radioValue="option1"
            radioSize="sm"
          ></sdga-form-input>

          <sdga-form-input
            name="radioGroup"
            type="radio"
            [label]="'FORM_LABELS.RADIO_OPTION_2' | translate"
            [description]="'DESCRIPTIONS.RADIO_OPTION_2' | translate"
            [control]="testInputs.get('radioGroup')!"
            [formSubmitted]="formSubmitted"
            radioValue="option2"
            radioSize="md"
          ></sdga-form-input>

          <sdga-form-input
            name="radioGroup"
            type="radio"
            [label]="'FORM_LABELS.RADIO_OPTION_3' | translate"
            [description]="'DESCRIPTIONS.RADIO_OPTION_3' | translate"
            [control]="testInputs.get('radioGroup')!"
            [formSubmitted]="formSubmitted"
            radioValue="option3"
            radioSize="lg"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Switch/Toggle Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.SWITCH_TOGGLE"></h2>

        <div class="space-y-4">
          <sdga-form-input
            name="switchSm"
            type="switch"
            [label]="'FORM_LABELS.SWITCH_SM' | translate"
            [description]="'DESCRIPTIONS.SWITCH_SM' | translate"
            [control]="testInputs.get('switchSm')!"
            [formSubmitted]="formSubmitted"
            switchSize="sm"
          ></sdga-form-input>

          <sdga-form-input
            name="switchMd"
            type="switch"
            [label]="'FORM_LABELS.SWITCH_MD' | translate"
            [description]="'DESCRIPTIONS.SWITCH_MD' | translate"
            [control]="testInputs.get('switchMd')!"
            [formSubmitted]="formSubmitted"
            switchSize="md"
          ></sdga-form-input>

          <sdga-form-input
            name="switchLg"
            type="switch"
            [label]="'FORM_LABELS.SWITCH_LG' | translate"
            [description]="'DESCRIPTIONS.SWITCH_LG' | translate"
            [control]="testInputs.get('switchLg')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            switchSize="lg"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Date Picker Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.DATE_PICKERS"></h2>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <sdga-form-input
            name="dateRange"
            [label]="'FORM_LABELS.DATE_RANGE_PICKER' | translate"
            [control]="testInputs.get('dateRange')!"
            [formSubmitted]="formSubmitted"
            [isDateRange]="true"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Textarea Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.TEXTAREA_SECTION"></h2>

        <sdga-form-input
          name="textarea"
          type="textarea"
          [label]="'FORM_LABELS.TEXTAREA_DESCRIPTION' | translate"
          [control]="testInputs.get('textarea')!"
          [formSubmitted]="formSubmitted"
          [required]="true"
          [placeholder]="'PLACEHOLDERS.ENTER_DESCRIPTION' | translate"
        ></sdga-form-input>
      </div>

      <!-- Checkbox Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.CHECKBOXES"></h2>

        <div class="space-y-4">
          <sdga-form-input
            name="checkbox-sm"
            type="checkbox"
            [label]="'FORM_LABELS.CHECKBOX_SM' | translate"
            [description]="'DESCRIPTIONS.CHECKBOX_SM' | translate"
            [control]="testInputs.get('checkboxSm')!"
            [formSubmitted]="formSubmitted"
            checkboxSize="sm"
          ></sdga-form-input>

          <sdga-form-input
            name="checkbox-md"
            type="checkbox"
            [label]="'FORM_LABELS.CHECKBOX_MD' | translate"
            [description]="'DESCRIPTIONS.CHECKBOX_MD' | translate"
            [control]="testInputs.get('checkboxMd')!"
            [formSubmitted]="formSubmitted"
            checkboxSize="md"
          ></sdga-form-input>

          <sdga-form-input
            name="checkbox-lg"
            type="checkbox"
            [label]="'FORM_LABELS.CHECKBOX_LG' | translate"
            [description]="'DESCRIPTIONS.CHECKBOX_LG' | translate"
            [control]="testInputs.get('checkboxLg')!"
            [formSubmitted]="formSubmitted"
            [required]="true"
            checkboxSize="lg"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Dropdown Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.DROPDOWNS"></h2>

        <!-- Basic Dropdowns -->
        <div class="space-y-4">
          <h3 class="text-base font-medium text-gray-700" translate="SUBSECTIONS.BASIC_DROPDOWNS"></h3>

          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Single Select Dropdown -->
            <sdga-from-dropdown
              name="singleSelect"
              [label]="'FORM_LABELS.SINGLE_SELECT' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_OPTION' | translate"
              [control]="testInputs.get('singleSelect')!"
              [formSubmitted]="formSubmitted"
              [required]="true"
              [options]="basicOptions"
              [clearable]="true"
              size="md"
            ></sdga-from-dropdown>

            <!-- Multi Select Dropdown -->
            <sdga-from-dropdown
              name="multiSelect"
              [label]="'FORM_LABELS.MULTI_SELECT' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_MULTIPLE' | translate"
              [control]="testInputs.get('multiSelect')!"
              [formSubmitted]="formSubmitted"
              [required]="true"
              [options]="basicOptions"
              [isMultiSelect]="true"
              [clearable]="true"
              [maxSelections]="3"
              size="md"
            ></sdga-from-dropdown>
          </div>
        </div>

        <!-- Size Variations -->
        <div class="space-y-4">
          <h3 class="text-base font-medium text-gray-700" translate="SUBSECTIONS.SIZE_VARIATIONS"></h3>

          <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
            <!-- Small Dropdown -->
            <sdga-from-dropdown
              name="smallDropdown"
              [label]="'FORM_LABELS.SMALL_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_OPTION' | translate"
              [control]="testInputs.get('smallDropdown')!"
              [formSubmitted]="formSubmitted"
              [options]="basicOptions"
              size="sm"
            ></sdga-from-dropdown>

            <!-- Medium Dropdown -->
            <sdga-from-dropdown
              name="mediumDropdown"
              [label]="'FORM_LABELS.MEDIUM_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_OPTION' | translate"
              [control]="testInputs.get('mediumDropdown')!"
              [formSubmitted]="formSubmitted"
              [options]="basicOptions"
              size="md"
            ></sdga-from-dropdown>

            <!-- Large Dropdown -->
            <sdga-from-dropdown
              name="largeDropdown"
              [label]="'FORM_LABELS.LARGE_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_OPTION' | translate"
              [control]="testInputs.get('largeDropdown')!"
              [formSubmitted]="formSubmitted"
              [options]="basicOptions"
              size="lg"
            ></sdga-from-dropdown>
          </div>
        </div>

        <!-- Advanced Features -->
        <div class="space-y-4">
          <h3 class="text-base font-medium text-gray-700" translate="SUBSECTIONS.ADVANCED_FEATURES"></h3>

          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Searchable Dropdown -->
            <sdga-from-dropdown
              name="searchableDropdown"
              [label]="'FORM_LABELS.SEARCHABLE_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.SEARCH_AND_SELECT' | translate"
              [control]="testInputs.get('searchableDropdown')!"
              [formSubmitted]="formSubmitted"
              [options]="largeOptionsList"
              [searchable]="true"
              [clearable]="true"
              size="md"
            ></sdga-from-dropdown>

            <!-- Dropdown with Icons and Descriptions -->
            <sdga-from-dropdown
              name="richDropdown"
              [label]="'FORM_LABELS.RICH_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_WITH_DETAILS' | translate"
              [control]="testInputs.get('richDropdown')!"
              [formSubmitted]="formSubmitted"
              [options]="richOptions"
              [clearable]="true"
              size="md"
            ></sdga-from-dropdown>
          </div>
        </div>

        <!-- Grouped Options -->
        <div class="space-y-4">
          <h3 class="text-base font-medium text-gray-700" translate="SUBSECTIONS.GROUPED_OPTIONS"></h3>

          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Single Select with Groups -->
            <sdga-from-dropdown
              name="groupedSingle"
              [label]="'FORM_LABELS.GROUPED_SINGLE' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_FROM_GROUPS' | translate"
              [control]="testInputs.get('groupedSingle')!"
              [formSubmitted]="formSubmitted"
              [groups]="groupedOptions"
              [clearable]="true"
              size="md"
            ></sdga-from-dropdown>

            <!-- Multi Select with Groups -->
            <sdga-from-dropdown
              name="groupedMulti"
              [label]="'FORM_LABELS.GROUPED_MULTI' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_MULTIPLE_GROUPS' | translate"
              [control]="testInputs.get('groupedMulti')!"
              [formSubmitted]="formSubmitted"
              [groups]="groupedOptions"
              [isMultiSelect]="true"
              [clearable]="true"
              size="md"
            ></sdga-from-dropdown>
          </div>
        </div>

        <!-- States and Validation -->
        <div class="space-y-4">
          <h3 class="text-base font-medium text-gray-700" translate="SUBSECTIONS.STATES_VALIDATION"></h3>

          <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
            <!-- Disabled Dropdown -->
            <sdga-from-dropdown
              name="disabledDropdown"
              [label]="'FORM_LABELS.DISABLED_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.DISABLED_STATE' | translate"
              [control]="testInputs.get('disabledDropdown')!"
              [formSubmitted]="formSubmitted"
              [options]="basicOptions"
              [disabled]="true"
              size="md"
            ></sdga-from-dropdown>

            <!-- Loading Dropdown -->
            <sdga-from-dropdown
              name="loadingDropdown"
              [label]="'FORM_LABELS.LOADING_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.LOADING_STATE' | translate"
              [control]="testInputs.get('loadingDropdown')!"
              [formSubmitted]="formSubmitted"
              [options]="basicOptions"
              [loading]="true"
              size="md"
            ></sdga-from-dropdown>

            <!-- Error State Dropdown -->
            <sdga-from-dropdown
              name="errorDropdown"
              [label]="'FORM_LABELS.ERROR_DROPDOWN' | translate"
              [placeholder]="'PLACEHOLDERS.SELECT_REQUIRED' | translate"
              [control]="testInputs.get('errorDropdown')!"
              [formSubmitted]="formSubmitted"
              [required]="true"
              [options]="basicOptions"
              size="md"
            ></sdga-from-dropdown>
          </div>
        </div>
      </div>

      <!-- Search Inputs Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.SEARCH_INPUTS"></h2>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <!-- Basic Search Input -->
          <sdga-form-input
            name="basicSearch"
            type="search"
            [label]="'FORM_LABELS.SEARCH_INPUT' | translate"
            [placeholder]="'PLACEHOLDERS.SEARCH_PLACEHOLDER' | translate"
            [control]="testInputs.get('basicSearch')!"
            [formSubmitted]="formSubmitted"
            [showSearchIcon]="true"
          />

          <!-- Search with Voice -->
          <sdga-form-input
            name="voiceSearch"
            type="search"
            [label]="'FORM_LABELS.SEARCH_WITH_VOICE' | translate"
            [placeholder]="'PLACEHOLDERS.VOICE_SEARCH_PLACEHOLDER' | translate"
            [control]="testInputs.get('voiceSearch')!"
            [formSubmitted]="formSubmitted"
            [showSearchIcon]="true"
            [enableVoiceSearch]="true"
          />

          <!-- Search without Icon -->
          <sdga-form-input
            name="simpleSearch"
            type="search"
            [label]="'Simple Search (No Icon)' | translate"
            [placeholder]="'PLACEHOLDERS.SEARCH_PLACEHOLDER' | translate"
            [control]="testInputs.get('simpleSearch')!"
            [formSubmitted]="formSubmitted"
            [showSearchIcon]="false"
            [enableVoiceSearch]="false"
          />

          <!-- Voice Only Search -->
          <sdga-form-input
            name="voiceOnlySearch"
            type="search"
            [label]="'Voice Only Search' | translate"
            [placeholder]="'PLACEHOLDERS.VOICE_SEARCH_PLACEHOLDER' | translate"
            [control]="testInputs.get('voiceOnlySearch')!"
            [formSubmitted]="formSubmitted"
            [showSearchIcon]="false"
            [enableVoiceSearch]="true"
          />
        </div>
      </div>

      <!-- Image Upload Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.IMAGE_UPLOAD"></h2>

        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <!-- Single Image Upload -->
          <div class="space-y-4">
            <h3 class="text-base font-medium text-gray-700" translate="SECTIONS.SINGLE_IMAGE_UPLOAD"></h3>
            <sdga-img-upload
              name="singleImage"
              [label]="'FORM_LABELS.SINGLE_IMAGE' | translate"
              [control]="testInputs.get('singleImage')!"
              [formSubmitted]="formSubmitted"
              [required]="true"
              [multiple]="false"
              [maxSize]="2097152"
              [allowedTypes]="['image/jpeg', 'image/png', 'image/webp']"
            ></sdga-img-upload>
          </div>

          <!-- Multiple Image Upload -->
          <div class="space-y-4">
            <h3 class="text-base font-medium text-gray-700" translate="SECTIONS.MULTIPLE_IMAGE_UPLOAD"></h3>
            <sdga-img-upload
              name="multipleImages"
              [label]="'FORM_LABELS.MULTIPLE_IMAGES' | translate"
              [control]="testInputs.get('multipleImages')!"
              [formSubmitted]="formSubmitted"
              [required]="false"
              [multiple]="true"
              [maxSize]="5242880"
              [allowedTypes]="['image/jpeg', 'image/png', 'image/gif', 'image/webp']"
            ></sdga-img-upload>
          </div>
        </div>
      </div>

      <!-- Special States Section -->
      <div class="space-y-4">
        <h2 class="pb-2 font-semibold text-gray-800 border-b border-gray-200 text-text-lg" translate="SECTIONS.SPECIAL_STATES"></h2>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <sdga-form-input
            name="disabled"
            type="text"
            [label]="'FORM_LABELS.DISABLED_FIELD' | translate"
            [control]="testInputs.get('disabled')!"
            [formSubmitted]="formSubmitted"
            [disabled]="true"
          ></sdga-form-input>

          <sdga-form-input
            name="readonly"
            type="text"
            [label]="'FORM_LABELS.READONLY_FIELD' | translate"
            [control]="testInputs.get('readonly')!"
            [formSubmitted]="formSubmitted"
            [readonly]="true"
          ></sdga-form-input>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex pt-6 space-x-4 border-t border-gray-200">
        <button
          type="submit"
          class="px-4 py-2 text-white transition-colors duration-200 rounded-md bg-sa-600 hover:bg-sa-700 focus:outline-none focus:ring-2 focus:ring-sa-500 focus:ring-offset-2"
          translate="GENERAL.SUBMIT"
        >
          Submit Form
        </button>

        <button
          type="button"
          (click)="resetForm()"
          class="px-4 py-2 text-white transition-colors duration-200 bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          translate="GENERAL.RESET"
        >
          Reset Form
        </button>
      </div>

      <!-- Form Status -->
      <div class="p-4 mt-6 bg-gray-100 rounded-md">
        <h3 class="mb-2 text-sm font-medium text-gray-700">Form Status:</h3>
        <p class="text-xs text-gray-600">Valid: {{ testInputs.valid }}</p>
        <p class="text-xs text-gray-600">Submitted: {{ formSubmitted }}</p>
        <p class="text-xs text-gray-600">Value: {{ testInputs.value | json }}</p>
      </div>
    </form>
  </div>
</div>