import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormInput } from '../../shared/components/inputs/form-input/form-input';
import { ImgUpload } from '../../shared/components/inputs/img-upload/img-upload';
import { FromDropdown, DropdownOption, DropdownGroup } from '../../shared/components/inputs/from-dropdown/from-dropdown';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateService, TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'sdga-inputs-test',
  imports: [CommonModule, FormInput, ImgUpload, FromDropdown, ReactiveFormsModule, TranslateModule],
  templateUrl: './inputs-test.html',
  styleUrl: './inputs-test.scss'
})
export class InputsTest {
  testInputs: FormGroup;
  formSubmitted = false;

  // Dropdown data
  basicOptions: DropdownOption[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4' },
    { value: 'option5', label: 'Option 5' }
  ];

  largeOptionsList: DropdownOption[] = [
    { value: 'country1', label: 'Saudi Arabia', description: 'Kingdom of Saudi Arabia' },
    { value: 'country2', label: 'United Arab Emirates', description: 'UAE' },
    { value: 'country3', label: 'Kuwait', description: 'State of Kuwait' },
    { value: 'country4', label: 'Qatar', description: 'State of Qatar' },
    { value: 'country5', label: 'Bahrain', description: 'Kingdom of Bahrain' },
    { value: 'country6', label: 'Oman', description: 'Sultanate of Oman' },
    { value: 'country7', label: 'Jordan', description: 'Hashemite Kingdom of Jordan' },
    { value: 'country8', label: 'Lebanon', description: 'Lebanese Republic' },
    { value: 'country9', label: 'Egypt', description: 'Arab Republic of Egypt' },
    { value: 'country10', label: 'Morocco', description: 'Kingdom of Morocco' }
  ];

  richOptions: DropdownOption[] = [
    { value: 'user', label: 'User Account', description: 'Standard user with basic permissions', icon: 'fas fa-user' },
    { value: 'admin', label: 'Administrator', description: 'Full system access and control', icon: 'fas fa-user-shield' },
    { value: 'moderator', label: 'Moderator', description: 'Content moderation privileges', icon: 'fas fa-user-check' },
    { value: 'guest', label: 'Guest User', description: 'Limited read-only access', icon: 'fas fa-user-clock', disabled: true }
  ];

  groupedOptions: DropdownGroup[] = [
    {
      label: 'Government Entities',
      options: [
        { value: 'ministry1', label: 'Ministry of Interior' },
        { value: 'ministry2', label: 'Ministry of Health' },
        { value: 'ministry3', label: 'Ministry of Education' }
      ]
    },
    {
      label: 'Private Sector',
      options: [
        { value: 'company1', label: 'Saudi Aramco' },
        { value: 'company2', label: 'SABIC' },
        { value: 'company3', label: 'STC' }
      ]
    },
    {
      label: 'Non-Profit Organizations',
      options: [
        { value: 'npo1', label: 'King Abdulaziz Foundation' },
        { value: 'npo2', label: 'Mohammed bin Rashid Foundation' }
      ]
    }
  ];

  constructor(private fb: FormBuilder, private translate: TranslateService) {
    this.testInputs = this.fb.group({
      text: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      number: ['', [Validators.required]],
      decimal: ['', [Validators.required]],
      textarea: ['', [Validators.required, Validators.maxLength(100)]],
      checkboxSm: [false],
      checkboxMd: [true],
      checkboxLg: [false, [Validators.requiredTrue]],
      radioGroup: ['', [Validators.required]],
      switchSm: [false],
      switchMd: [true],
      switchLg: [false, [Validators.requiredTrue]],
      singleDate: ['', [Validators.required]],
      dateRange: [''],
      singleImage: [null, [Validators.required]],
      multipleImages: [[]],
      disabled: [{value: 'This is disabled', disabled: true}],
      readonly: ['This is readonly'],
      // Search form controls
      basicSearch: [''],
      voiceSearch: [''],
      simpleSearch: [''],
      voiceOnlySearch: [''],
      // Dropdown form controls
      singleSelect: ['', [Validators.required]],
      multiSelect: [[], [Validators.required]],
      smallDropdown: [''],
      mediumDropdown: [''],
      largeDropdown: [''],
      searchableDropdown: [''],
      richDropdown: [''],
      groupedSingle: [''],
      groupedMulti: [[]],
      disabledDropdown: [{value: 'option1', disabled: true}],
      loadingDropdown: [''],
      errorDropdown: ['', [Validators.required]]
    });
  }

  onSubmit() {
    this.formSubmitted = true;
    if (this.testInputs.valid) {
      console.log('Form submitted:', this.testInputs.value);
    } else {
      console.log('Form has errors:', this.testInputs.errors);
    }
  }

  resetForm() {
    this.formSubmitted = false;
    this.testInputs.reset();
  }


}
