import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Breadcrumb } from "../../shared/components/breadcrumb/breadcrumb";
import { Navbar } from "../../shared/components/navbar/navbar";
import { CommonModule } from '@angular/common';
import { SecondNavbar } from "../../shared/components/second-navbar/second-navbar";
import { PreviewService } from '../../core/services/preview.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'sdga-default-layout',
  imports: [RouterOutlet, Breadcrumb, Navbar, CommonModule, SecondNavbar],
  templateUrl: './default-layout.html',
  styleUrl: './default-layout.scss'
})
export class DefaultLayout {
  showPreview = true;

  private previewSub?: Subscription;
  constructor(private previewService: PreviewService) {
    this.previewSub = this.previewService.preview$.subscribe(val => this.showPreview = val);
  }
  ngOnDestroy(): void {
    this.previewSub?.unsubscribe();
  }
}
