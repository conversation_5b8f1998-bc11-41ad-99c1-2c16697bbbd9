<!-- With background -->
<!-- <sdga-content-switcher [items]="['Item', 'Item', 'Item', 'Item']" [hasBackground]="true" size="md"
    (switched)="onSwitch($event)"></sdga-content-switcher> -->

<!-- RTL without background -->
<div class="grid grid-flow-col col-start-1 grid-rows-3 row-start-1 gap-4 leading-6 text-center rounded-lg">
    <div class="grid row-span-3 p-4 border border-gray-300 rounded-sm">
        <sdga-content-switcher [items]="['Item 1', 'Item 2', 'Item 3', 'Item 4']" size="sm"
            (switched)="onSwitchOne($event)" [hasBackground]="false">
        </sdga-content-switcher>
        <div *ngIf="selectedIndex1 === 0">content 1</div>
        <div *ngIf="selectedIndex1 === 1">content 2</div>
        <div *ngIf="selectedIndex1 === 2">content 3</div>
        <div *ngIf="selectedIndex1=== 3">content 4</div>
    </div>
    <div class="grid col-span-2 p-4 border border-gray-300 rounded-sm">02</div>
    <div class="grid col-span-2 row-span-2 p-4 bg-black border border-gray-300 rounded-sm">
        <sdga-content-switcher [items]="['Item 1', 'Item 2', 'Item 3', 'Item 4']" size="lg"
            (switched)="onSwitchTwo($event)" [hasBackground]="true">
        </sdga-content-switcher>
        <div class="text-white" *ngIf="selectedIndex2 === 0">content 1 (oncolor)</div>
        <div class="text-white" *ngIf="selectedIndex2 === 1">content 2 (oncolor)</div>
        <div class="text-white" *ngIf="selectedIndex2 === 2">content 3 (oncolor)</div>
        <div class="text-white" *ngIf="selectedIndex2 === 3">content 4 (oncolor)</div>
    </div>
</div>