# SDGA Tree Component

A reusable hierarchical tree component with collapsible layers following the Saudi Digital Government Design System specifications.

## Features

- ✅ **SDGA Design System Compliant**: Follows official design specifications
- ✅ **Tailwind CSS Only**: No custom CSS dependencies
- ✅ **RTL/LTR Support**: Full bidirectional text support
- ✅ **Hierarchical Structure**: Support for unlimited nesting levels
- ✅ **Expand/Collapse**: Interactive node expansion with smooth animations
- ✅ **Multiple Expansion Modes**: Single or multiple node expansion
- ✅ **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- ✅ **TypeScript**: Full type safety with comprehensive interfaces
- ✅ **Configurable**: Multiple configuration options and behaviors
- ✅ **Event Handling**: Rich event system for node interactions
- ✅ **Custom Icons**: Support for custom node icons
- ✅ **Badges/Counts**: Optional badges and count displays
- ✅ **Disabled States**: Support for disabled nodes

## Basic Usage

```typescript
import { Tree, TreeNode, TreeConfig } from '@shared/components/tree/tree';

@Component({
  selector: 'app-example',
  imports: [Tree],
  template: `
    <sdga-tree
      [nodes]="treeData"
      [config]="treeConfig"
      (nodeClick)="onNodeClick($event)"
      (nodeExpand)="onNodeExpand($event)"
      (nodeCollapse)="onNodeCollapse($event)">
    </sdga-tree>
  `
})
export class ExampleComponent {
  treeData: TreeNode[] = [
    {
      id: '1',
      label: 'Parent Node',
      children: [
        {
          id: '1-1',
          label: 'Child Node',
          children: [
            { id: '1-1-1', label: 'Grandchild Node' }
          ]
        }
      ]
    }
  ];

  treeConfig: TreeConfig = {
    allowMultipleExpansion: true,
    showIcons: true,
    showConnectors: true,
    expandOnClick: false,
    initialExpandLevel: 1,
    rtlSupport: true
  };

  onNodeClick(event: TreeNodeClickEvent) {
    console.log('Node clicked:', event.node);
  }

  onNodeExpand(event: TreeNodeExpandEvent) {
    console.log('Node expanded:', event.node);
  }

  onNodeCollapse(event: TreeNodeExpandEvent) {
    console.log('Node collapsed:', event.node);
  }
}
```

## API Reference

### Inputs

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `nodes` | `TreeNode[]` | `[]` | Array of tree nodes to display |
| `config` | `TreeConfig` | See below | Configuration options for tree behavior |

### Outputs

| Event | Type | Description |
|-------|------|-------------|
| `nodeClick` | `TreeNodeClickEvent` | Emitted when a node is clicked |
| `nodeExpand` | `TreeNodeExpandEvent` | Emitted when a node is expanded |
| `nodeCollapse` | `TreeNodeExpandEvent` | Emitted when a node is collapsed |

### Interfaces

#### TreeNode

```typescript
interface TreeNode {
  id: string | number;           // Unique identifier for the node
  label: string;                 // Display text for the node
  children?: TreeNode[];         // Child nodes (optional)
  expanded?: boolean;            // Initial expansion state (optional)
  disabled?: boolean;            // Whether the node is disabled (optional)
  icon?: string;                 // Icon name for the node (optional)
  data?: any;                    // Additional data for the node (optional)
  level?: number;                // Calculated nesting level (auto-calculated)
}
```

#### TreeConfig

```typescript
interface TreeConfig {
  allowMultipleExpansion?: boolean;  // Allow multiple nodes expanded (default: true)
  showIcons?: boolean;               // Show expand/collapse icons (default: true)
  showConnectors?: boolean;          // Show connecting lines (default: true)
  expandOnClick?: boolean;           // Expand on label click vs icon only (default: false)
  initialExpandLevel?: number;       // Auto-expand to this level (default: 0)
  rtlSupport?: boolean;              // Enable RTL support (default: true)
}
```

#### Events

```typescript
interface TreeNodeClickEvent {
  node: TreeNode;
  event: MouseEvent;
}

interface TreeNodeExpandEvent {
  node: TreeNode;
  expanded: boolean;
}
```

## Configuration Examples

### Basic Tree (Default)
```typescript
const basicConfig: TreeConfig = {
  allowMultipleExpansion: true,
  showIcons: true,
  showConnectors: true,
  expandOnClick: false,
  initialExpandLevel: 0,
  rtlSupport: true
};
```

### Single Expansion Mode
```typescript
const singleExpansionConfig: TreeConfig = {
  allowMultipleExpansion: false,  // Only one node expanded at a time
  showIcons: true,
  showConnectors: true,
  expandOnClick: true,            // Click anywhere to expand
  initialExpandLevel: 0,
  rtlSupport: true
};
```

### Minimal Tree
```typescript
const minimalConfig: TreeConfig = {
  allowMultipleExpansion: true,
  showIcons: false,               // No expand/collapse icons
  showConnectors: false,          // No connecting lines
  expandOnClick: true,            // Click anywhere to expand
  initialExpandLevel: 0,
  rtlSupport: true
};
```

### Auto-Expanded Tree
```typescript
const autoExpandConfig: TreeConfig = {
  allowMultipleExpansion: true,
  showIcons: true,
  showConnectors: true,
  expandOnClick: false,
  initialExpandLevel: 2,          // Auto-expand first 2 levels
  rtlSupport: true
};
```

## Data Structure Examples

### Simple Tree
```typescript
const simpleTree: TreeNode[] = [
  {
    id: 'page-1',
    label: 'Page Section',
    children: [
      { id: 'page-1-1', label: 'Nested Page Section' },
      { id: 'page-1-2', label: 'Nested Page Section' }
    ]
  },
  { id: 'page-2', label: 'Page Section' }
];
```

### Tree with Icons and Badges
```typescript
const complexTree: TreeNode[] = [
  {
    id: 'folder-1',
    label: 'Documents',
    icon: 'folder',
    data: { count: 15 },
    children: [
      {
        id: 'file-1',
        label: 'Report.pdf',
        icon: 'file',
        data: { count: 1 }
      }
    ]
  },
  {
    id: 'folder-2',
    label: 'Archive',
    icon: 'folder',
    disabled: true,
    data: { count: 0 }
  }
];
```

### Arabic/RTL Tree
```typescript
const arabicTree: TreeNode[] = [
  {
    id: 'ar-1',
    label: 'قسم الصفحة',
    children: [
      { id: 'ar-1-1', label: 'قسم الصفحة المتداخل' },
      { id: 'ar-1-2', label: 'قسم الصفحة المتداخل' }
    ]
  }
];
```

## Styling and Theming

The component uses SDGA design system colors and follows Tailwind CSS utility classes:

- **Primary Color**: `sa-500` (Saudi Authority green)
- **Text Colors**: `gray-900`, `gray-600`, `gray-400`
- **Background Colors**: `sa-50`, `gray-50`, `white`
- **Border Colors**: `sa-500`, `gray-200`, `gray-300`
- **Spacing**: Consistent with SDGA spacing scale
- **Border Radius**: `4px` (rounded-sm) following SDGA specifications

## Accessibility Features

- **ARIA Labels**: Proper `aria-expanded`, `aria-level`, `aria-selected` attributes
- **Keyboard Navigation**: Enter and Space key support for expansion
- **Screen Reader Support**: Semantic HTML structure with `role="tree"` and `role="treeitem"`
- **Focus Management**: Visible focus indicators and proper tab order
- **Disabled States**: Proper handling of disabled nodes

## RTL Support

The component automatically supports RTL layouts:

```html
<!-- RTL Example -->
<div dir="rtl">
  <sdga-tree [nodes]="arabicTreeData" [config]="config"></sdga-tree>
</div>
```

- Icons and indentation automatically flip for RTL
- Text alignment adjusts based on direction
- Connector lines position correctly in RTL mode

## Methods

The component exposes several public methods for programmatic control:

```typescript
// Get component reference
@ViewChild(Tree) treeComponent!: Tree;

// Expand all nodes
this.treeComponent.expandAll();

// Collapse all nodes
this.treeComponent.collapseAll();

// Check if node is expanded
const isExpanded = this.treeComponent.isNodeExpanded('node-id');

// Check if node has children
const hasChildren = this.treeComponent.hasChildren(node);
```

## Examples

Visit `/examples/tree-example` to see comprehensive examples including:

- Basic tree with multiple expansion
- Arabic tree with RTL support
- Single expansion mode
- Auto-expanded tree
- Complex tree with icons and badges
- Minimal tree without decorations

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- Angular 17+
- @ngx-translate/core (for internationalization)
- Tailwind CSS 3+
- tailwindcss-rtl plugin

## Contributing

When contributing to this component:

1. Follow SDGA design system specifications
2. Use only Tailwind CSS utility classes
3. Ensure RTL/LTR support
4. Add comprehensive tests
5. Update documentation
6. Test accessibility features
