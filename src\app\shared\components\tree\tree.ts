import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, TemplateRef } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Simple TreeNode interface
export interface TreeNode {
  id: string | number;
  label: string;
  labelKey?: string; // Translation key for the label
  content?: TemplateRef<any>; // Template reference for content sharing
  children?: TreeNode[];
  expanded?: boolean;
  disabled?: boolean;
}

@Component({
  selector: 'sdga-tree',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './tree.html',
  styleUrl: './tree.scss'
})
export class Tree {
  @Input() nodes: TreeNode[] = [];
  @Input() level: number = 0; // Track tree level (0 = first level)
  @Output() nodeClick = new EventEmitter<TreeNode>();

  selectedNodeId: string | number | undefined;

  /**
   * Check if this is the first level tree
   */
  get isFirstLevel(): boolean {
    return this.level === 0;
  }

  /**
   * Check if node is a main title (root level or second level with children)
   */
  isMainTitle(node: TreeNode): boolean {
    return (this.level === 0 && this.hasChildren(node)) || (this.level === 1 && this.hasChildren(node));
  }

  constructor(private translateService: TranslateService) {}

  /**
   * Get translated label for a node
   */
  getNodeLabel(node: TreeNode): string {
    if (node.labelKey) {
      return this.translateService.instant(node.labelKey);
    }
    return node.label || '';
  }

  /**
   * Check if node has children
   */
  hasChildren(node: TreeNode): boolean {
    return !!(node.children && node.children.length > 0);
  }

  /**
   * Check if node is expanded
   */
  isExpanded(node: TreeNode): boolean {
    return !!node.expanded;
  }

  /**
   * Check if node is selected
   */
  isSelected(node: TreeNode): boolean {
    return this.selectedNodeId === node.id;
  }

  /**
   * Check if node has content template
   */
  hasContent(node: TreeNode): boolean {
    return !!node.content;
  }

  /**
   * Toggle node expansion
   */
  toggleNode(node: TreeNode): void {
    if (!this.hasChildren(node) || node.disabled) return;
    node.expanded = !node.expanded;
  }

  /**
   * Check if node is a content node (has template content)
   */
  isContentNode(node: TreeNode): boolean {
    return !!node.content;
  }

  /**
   * Check if node can be collapsed (has children but no content)
   */
  canBeCollapsed(node: TreeNode): boolean {
    return this.hasChildren(node) && !this.isContentNode(node);
  }

  /**
   * Handle node title click (for expand/collapse and selection)
   */
  onNodeTitleClick(node: TreeNode): void {
    if (node.disabled) return;

    // Only toggle expansion if node can be collapsed
    if (this.canBeCollapsed(node)) {
      this.toggleNode(node);

      // Auto-expand content children when parent is expanded
      if (node.expanded && node.children) {
        this.autoExpandContentChildren(node.children);
      }
    }

    // Always select the node and emit click event
    this.selectedNodeId = node.id;
    this.nodeClick.emit(node);
  }

  /**
   * Handle node click (for backward compatibility)
   */
  onNodeClick(node: TreeNode): void {
    this.onNodeTitleClick(node);
  }

  /**
   * Auto-expand content children when parent is expanded
   */
  private autoExpandContentChildren(children: TreeNode[]): void {
    children.forEach(child => {
      // If child has content (is a content node), expand it automatically
      if (this.hasContent(child)) {
        child.expanded = true;
      }
      // If child has children, recursively check them
      else if (child.children) {
        this.autoExpandContentChildren(child.children);
      }
    });
  }

  /**
   * Get CSS classes for a node
   */
  getNodeClasses(node: TreeNode): string {
    const classes = [];

    if (this.isSelected(node)) {
      classes.push( 'text-sa-800');
    }

    if (node.disabled) {
      classes.push('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
    }

    return classes.join(' ');
  }



  /**
   * Get selected node
   */
  getSelectedNode(): TreeNode | null {
    if (!this.selectedNodeId) return null;
    return this.findNodeById(this.nodes, this.selectedNodeId);
  }

  /**
   * Find node by ID recursively
   */
  private findNodeById(nodes: TreeNode[], id: string | number): TreeNode | null {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = this.findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * Check if selected node has content
   */
  hasSelectedContent(): boolean {
    const selectedNode = this.getSelectedNode();
    return selectedNode ? this.hasContent(selectedNode) : false;
  }

  /**
   * Get selected node content template
   */
  getSelectedContent(): TemplateRef<any> | null {
    const selectedNode = this.getSelectedNode();
    return selectedNode?.content || null;
  }
}
