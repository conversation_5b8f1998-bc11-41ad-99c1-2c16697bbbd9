// App Component Styles
:host {
  display: block;
  min-height: 100vh;
}

// Header styles
header {
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

// Main content styles
main {
  flex: 1;
}

// RTL support
[dir="rtl"] {
  .space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
  }
}

.zoom-wrapper {
  transition: transform 0.2s;
  transform: scale(var(--zoom-level, 1));
  transform-origin: top center;
}
