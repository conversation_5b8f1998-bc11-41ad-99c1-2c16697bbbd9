<!-- Menu Panel -->
<div *ngIf="!closed" class="w-full max-w-sm p-4 space-y-6 bg-white border rounded-md shadow-2xl border-gray-default">
   <!-- ✅ Optional Menu Header -->
  <div *ngIf="header" class="flex items-start justify-between">
    <div class="space-y-0.5">
      <div class="flex items-center">
        <sdga-svg-icon [name]="header.icon" class="text-green-600"></sdga-svg-icon>
        <h2 class="text-base font-semibold text-default-100">{{ header.title }}</h2>
      </div>
      <p *ngIf="header.subtitle" class="text-xs text-gray-500">
        {{ header.subtitle }}
      </p>
    </div>
    <button *ngIf="header.closeable" (click)="closeMenu()" class="hover:text-black">
      <sdga-svg-icon name="close-menu"></sdga-svg-icon>
    </button>
  </div>
  <hr *ngIf="header" class="my-2 border-gray-default">

  <!-- Menu Sections -->

  <ng-container *ngFor="let group of menuGroups; let i = index">
    <div>
      <h4 class="mb-2 font-bold uppercase text-text-md text-default-100">{{ group.label }}</h4>
      <ul class="space-y-1">
        <li *ngFor="let item of group.items"
          class="flex items-center justify-between px-3 py-2 text-sm text-gray-700 rounded cursor-pointer hover:bg-gray-100">
          <span class="flex items-center gap-2">
            <sdga-svg-icon [name]="item.icon"></sdga-svg-icon>
            {{ item.label }}
          </span>
          <ng-container *ngIf="item.checked">
            <sdga-svg-icon name="check"></sdga-svg-icon>
          </ng-container>
          <ng-container *ngIf="item.switch">
            <sdga-form-input name="switchSm" type="switch" [control]="testInputs?.get('switchSm')!"
              [formSubmitted]="formSubmitted" switchSize="sm"></sdga-form-input>
          </ng-container>
          <ng-container *ngIf="item.count">
            <span class="text-xs font-semibold text-gray-400">{{ item.count }}</span>
          </ng-container>
<ng-container *ngIf="item.arrow">
  <!-- LTR: right arrow -->
  <sdga-svg-icon
    *ngIf="direction === 'ltr'"
    name="arrow-right-long"
  ></sdga-svg-icon>

  <!-- RTL: left arrow -->
  <sdga-svg-icon
    *ngIf="direction === 'rtl'"
    name="arrow-left-long"
  ></sdga-svg-icon>
</ng-container>

          <ng-container *ngIf="item.badge">
            <span [ngClass]="item.badgeClass">
              {{ item.badge }}
            </span>
          </ng-container>
        </li>
      </ul>
    </div>
    <hr class="my-2 border-gray-default" *ngIf="i < menuGroups.length - 1">
  </ng-container>
</div>
