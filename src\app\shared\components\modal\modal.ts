import { Component, Input } from '@angular/core';
import { SvgIcon } from '../svg-icon/svg-icon';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'sdga-modal',
  imports: [SvgIcon, CommonModule],
  templateUrl: './modal.html',
  styleUrl: './modal.scss',
})
export class Modal {
  @Input() title: string = '';
  @Input() content: string = '';
  @Input() buttons: { type: 'outline' , label: string; onClick: () => void }[] = [];

  iconClass: string = ' inline-block  p-3 rounded-full';

  @Input() isVertical: boolean = true;
  @Input() iconName: string = 'successIcon';
  @Input() ButtonClose: string = 'ButtonClose';
  @Input() closeIconClass: string = '';


  get containerClasses(): string[] {
    const classes = ['flex', 'rounded-md', 'p-6', 'bg-white' , ' flex-col'];
    return classes;
  }

  get contentClasses(): string[] {
    return [ 'text-gray-500', 'px-5'];
  }
  get contentTitleClasse(): string[] {
    return ['font-semibold', 'text-gray-800', 'text-lg'];
  }
  get TextContent(): string[] {
    return ['font-normal', 'text-gray-700' , 'text-sm'];
  }

}
