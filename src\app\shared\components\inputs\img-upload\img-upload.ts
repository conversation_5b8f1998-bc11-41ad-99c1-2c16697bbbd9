import { CommonModule } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, Output, SimpleChanges, ViewChild, forwardRef } from '@angular/core';
import { AbstractControl, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateService, TranslateModule } from '@ngx-translate/core';

interface PreviewImage {
  url: string;
  name: string;
  file: File;
  isUploading?: boolean;
  progress?: number;
}

@Component({
  selector: 'sdga-img-upload',
  imports: [CommonModule, TranslateModule],
  templateUrl: './img-upload.html',
  styleUrl: './img-upload.scss'
})
export class ImgUpload {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @Input() required: boolean = false;
  @Input() control: AbstractControl | undefined;
  @Output() fileUploaded = new EventEmitter(); // Emits the uploaded file URL
  @Input() formSubmitted: boolean = false;
  @Input() name: string = '';
  @Input() label: string | undefined;
  @Input() multiple: boolean = false; // ✅ Multiple file upload support
  @Input() allowedTypes: string[] = ['image/png', 'image/jpeg', 'image/jpg']; // ✅ Allowed file types
  @Input() maxSize: number = 5 * 1024 * 1024; // ✅ Max file size in bytes

  // Component state
  file: File | null = null;
  files: File[] = []; // ✅ For multiple file support
  fileName: string | null = null;
  isDisabled: boolean = false;
  translatedLabel: string = '';
  errorMessage: string = ''; // ✅ Stores validation errors
  isDragOver: boolean = false; // ✅ Drag and drop state
  isFocused: boolean = false; // ✅ Focus state for animated underline
  isUploading: boolean = false; // ✅ Upload progress state
  uploadProgress: number = 0; // ✅ Upload progress percentage
  previewImages: PreviewImage[] = []; // ✅ Preview images array

  private onChange: (value: File | File[] | null) => void = () => {};
  private onTouched: () => void = () => {};
  @Input() fileUrl: string | undefined;
  fileData: any;

  constructor(
    private translate: TranslateService,
    private http: HttpClient,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (this.label) this.translatedLabel = this.translate.instant(this.label);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['fileUrl'] && changes['fileUrl'].currentValue) {
      this.fileData = changes['fileUrl'].currentValue;
      this.getFileUrl();
    }
  }

  writeValue(file: File | null): void {
    this.file = file || null;
    this.control?.updateValueAndValidity();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  /** ✅ Handles file selection */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      if (this.multiple) {
        this.handleMultipleFiles(Array.from(input.files));
      } else {
        this.validateAndAddFile(input.files[0]);
      }
    } else {
      this.clearFiles();
    }
    this.onTouched();
  }

  /** ✅ Handles multiple file selection */
  private handleMultipleFiles(files: File[]): void {
    for (const file of files) {
      this.validateAndAddFile(file);
    }
  }

  /** ✅ Validates and adds a single file */
  private validateAndAddFile(file: File): void {
    this.errorMessage = '';

    if (!this.allowedTypes.includes(file.type)) {
      this.translate.get('IMG_UPLOAD.INVALID_FILE_TYPE', {
        allowedTypes: this.getAllowedTypesText()
      }).subscribe((res) => {
        this.errorMessage = res;
      });
      return;
    }

    if (file.size > this.maxSize) {
      this.translate.get('IMG_UPLOAD.FILE_SIZE_EXCEEDED', {
        maxSize: this.getMaxSizeText()
      }).subscribe((res) => {
        this.errorMessage = res;
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      const previewImage: PreviewImage = {
        url: e.target?.result as string,
        name: file.name,
        file: file,
        isUploading: false,
        progress: 0
      };

      if (this.multiple) {
        this.previewImages.push(previewImage);
        this.files.push(file);
        this.onChange(this.files);
      } else {
        this.previewImages = [previewImage];
        this.file = file;
        this.onChange(file);
      }

      this.cdr.detectChanges();
    };
    reader.readAsDataURL(file);
  }

  /** ✅ Clears all files */
  private clearFiles(): void {
    this.file = null;
    this.files = [];
    this.fileName = null;
    this.previewImages = [];
    this.errorMessage = '';
    this.onChange(this.multiple ? [] : null);
  }

  /** ✅ Removes a specific image */
  removeImage(index: number): void {
    if (this.multiple) {
      this.previewImages.splice(index, 1);
      this.files.splice(index, 1);
      this.onChange(this.files);
    } else {
      this.clearFiles();
    }
  }

  /** ✅ Gets formatted allowed types text */
  getAllowedTypesText(): string {
    return this.allowedTypes
      .map(type => type.split('/')[1].toUpperCase())
      .join(', ');
  }

  /** ✅ Gets formatted max size text */
  getMaxSizeText(): string {
    const sizeInMB = this.maxSize / (1024 * 1024);
    return `${sizeInMB}MB`;
  }

  /** ✅ Focus handlers for animated underline */
  onFocus(): void {
    this.isFocused = true;
  }

  onBlur(): void {
    this.isFocused = false;
    this.onTouched();
  }

  clearImage() {
    this.clearFiles();
    this.fileData = null;
    let image = {
      id: '',
      fileName: '',
      folder: '',
      serverFileName: '',
      url: '',
      extension: '',
    };
    this.fileUploaded.emit(image);
    this.fileUrl = '';
  }

  /** ✅ Uploads the selected file */
  private uploadFile(): void {
    if (!this.file) return;
    // this.imageUploadService.uploadImage(this.file).subscribe({
    //   next: (response: UploadImageResponse) => {
    //     this.fileUploaded.emit(response.data);
    //     this.fileName = response.data.fileName;
    //     this.fileUrl = response.data.url;
    //     this.getFileUrl();
    //   },
    //   error: () => {
    //     this.fileName = null;
    //     this.fileUrl = '';
    //   },
    // });
  }

  getFileUrl() {
    // const token = this.authService.getToken();
    // this.http.get(this.fileUrl, {
    //     headers: new HttpHeaders({
    //       Authorization: `Bearer ${token}`,
    //     }),
    //     responseType: 'blob',
    //   })
    //   .subscribe((blob: Blob | MediaSource) => {
    //     const objectUrl = URL.createObjectURL(blob);
    //     this.fileData = objectUrl; // bind to [src] in your HTML

    //     this.cdr.detectChanges();
    //   });
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
  }

  /** ✅ Handles file drop */
  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;

    if (event.dataTransfer?.files.length) {
      if (this.multiple) {
        this.handleMultipleFiles(Array.from(event.dataTransfer.files));
      } else {
        this.validateAndAddFile(event.dataTransfer.files[0]);
      }
    } else {
      this.clearFiles();
    }
    this.onTouched();
  }
}


