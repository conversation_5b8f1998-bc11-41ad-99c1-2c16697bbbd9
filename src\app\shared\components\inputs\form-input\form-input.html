<div
  class="w-full"
  [ngClass]="{
    'border border-dashed  rounded-lg p-4 bg-purple-25': type == 'checkbox',
    'space-y-1': type != 'checkbox'
  }"
>
  <!-- Checkbox Layout -->
  <div *ngIf="type === 'checkbox'" class="flex gap-3 sm:gap-2"
       [ngClass]="{'text-red-600': formSubmitted && control?.errors}">
    <!-- Checkbox Input Container -->
    <div class="relative flex-shrink-0"    [ngClass]="{
          'h-4 w-4': !checkboxSize || checkboxSize === 'sm',
          'h-5 w-5': checkboxSize === 'md',
          'h-6 w-6': checkboxSize === 'lg',
        }">
      <input
        [type]="type"
        [placeholder]="placeholder || label"
        [class]="class"
        [required]="required"
        [disabled]="disabled"
        [readonly]="isReadOnly || readonly"
        [attr.name]="name"
        [value]="value"
        [checked]="value"
        [min]="min"
        [max]="max"
        (keydown)="preventIfDate($event)"
        class="block transition-all duration-200 bg-white border-2 border-gray-400 rounded appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        [ngClass]="{
          'h-4 w-4': !checkboxSize || checkboxSize === 'sm',
          'h-5 w-5': checkboxSize === 'md',
          'h-6 w-6': checkboxSize === 'lg',
          'border-sa-600 bg-sa-600': value,
          'border-gray-400 bg-white': !value,
          'border-error-500': formSubmitted && control?.errors
        }"
        (change)="onInputChange($event)"
        (blur)="onBlur()"
        (click)="
          type === 'checkbox' && unClickable ? $event.preventDefault() : null
        "
      />

      <!-- Checkbox checkmark icon - positioned absolutely over the input -->
      <svg
        *ngIf="value"
        class="absolute text-white pointer-events-none transform -translate-x-1/2 -translate-y-1/2 top-[50%] left-[50%] "
        [ngClass]="{
          'w-2 h-2 top-1 left-1': !checkboxSize || checkboxSize === 'sm',
          'w-2 h-2 top-1.5 left-1.5': checkboxSize === 'md',
          'w-3 h-3 top-1.5 left-1.5': checkboxSize === 'lg'
        }"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
    </div>

    <!-- Label and Description Container -->
    <div class="flex-1 min-w-0 text-left rtl:text-right">
      <label
        [attr.for]="name"
        class="block text-sm font-medium text-gray-900 cursor-pointer"
        [ngClass]="{
          'text-error-600': formSubmitted && control?.errors
        }"
      >
        {{ label }}<span *ngIf="required" class="ml-1 text-error-500">*</span>
      </label>

      <!-- Description Text -->
      <p *ngIf="description" class="mt-1 text-sm text-gray-600">
        {{ description }}
      </p>

      <!-- Checkbox Error Messages -->
      <div *ngIf="formSubmitted && control?.errors" class="mt-2">
        <div
          *ngIf="control?.errors?.['required']"
          class="flex items-center text-xs text-error-600"
        >
          <svg class="flex-shrink-0 w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span>This selection is required</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Radio Button Input -->
  <div *ngIf="type === 'radio'" class="flex gap-3 sm:gap-2 ltr:flex-row rtl:flex-row-reverse"
       [ngClass]="{'text-red-600': formSubmitted && control?.errors}">
    <!-- Radio Input Container -->
    <div class="relative flex-shrink-0 order-1 rtl:order-2"  [ngClass]="{
          'h-4 w-4': !radioSize || radioSize === 'sm',
          'h-5 w-5': radioSize === 'md',
          'h-6 w-6': radioSize === 'lg',
          
        }">
      <input
        [type]="type"
        [class]="class"
        [required]="required"
        [disabled]="disabled"
        [readonly]="isReadOnly || readonly"
        [attr.name]="name"
        [value]="radioValue || value"
        [checked]="isRadioSelected()"
        class="block transition-all duration-200 bg-white border-2 border-gray-400 rounded-full appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        [ngClass]="{
          'h-4 w-4': !radioSize || radioSize === 'sm',
          'h-5 w-5': radioSize === 'md',
          'h-6 w-6': radioSize === 'lg',
          'border-sa-600': isRadioSelected(),
          'border-gray-400': !isRadioSelected(),
          'border-error-500': formSubmitted && control?.errors
        }"
        (click)="onRadioClick($event)"
        (blur)="onBlur()"
      />

      <!-- Radio button inner circle -->
      <div
        *ngIf="isRadioSelected()"
        class="absolute z-10 transform -translate-x-1/2 -translate-y-1/2 top-[50%] left-[50%] rounded-full pointer-events-none bg-sa-600"
        [ngClass]="{
          'w-2 h-2  ': !radioSize || radioSize === 'sm',
          'w-2.5 h-2.5 ': radioSize === 'md',
          'w-3 h-3 ': radioSize === 'lg'
        }"
      ></div>
    </div>

    <!-- Label and Description Container -->
    <div class="flex-1 order-2 min-w-0 text-left rtl:text-right rtl:order-1">
      <label
        [attr.for]="name"
        class="block text-sm font-medium text-gray-900 cursor-pointer"
        [ngClass]="{
          'text-error-600': formSubmitted && control?.errors
        }"
        (click)="selectRadio()"
      >
        {{ label }}<span *ngIf="required" class="ltr:ml-1 rtl:mr-1 text-error-500">*</span>
      </label>
      <p *ngIf="description" class="mt-1 text-sm text-gray-600">{{ description }}</p>

      <!-- Radio Error Messages -->
      <div *ngIf="formSubmitted && control?.errors" class="mt-2">
        <div
          *ngIf="control?.errors?.['required']"
          class="flex items-center text-xs text-error-600"
        >
          <svg class="flex-shrink-0 w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span>This selection is required</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Switch/Toggle Input -->
  <div *ngIf="type === 'switch'" class="flex gap-3 sm:gap-2"
       [ngClass]="{'text-red-600': formSubmitted && control?.errors}">
    <!-- Switch Input Container -->
    <div class="relative flex-shrink-0">
      <input
        type="checkbox"
        [class]="class"
        [required]="required"
        [disabled]="disabled"
        [readonly]="isReadOnly || readonly"
        [attr.name]="name"
        [checked]="isSwitchOn()"
        class="sr-only"
         [ngClass]="{
          'w-10 h-5': switchSize === 'sm',
          'w-12 h-6': switchSize === 'md',
          'w-14 h-7': switchSize === 'lg',
        }"
        (change)="onSwitchChange($event)"
        (blur)="onBlur()"
      />

      <!-- Switch Toggle Button -->
      <label
        class="relative inline-block transition-all duration-200 rounded-full cursor-pointer"
        [ngClass]="{
          'w-10 h-5': switchSize === 'sm',
          'w-12 h-6': switchSize === 'md',
          'w-14 h-7': switchSize === 'lg',
          'opacity-50 cursor-not-allowed': disabled
        }"
        [attr.data-state]="isSwitchOn() ? 'ON' : 'OFF'"
        (click)="toggleSwitch()"
      >
        <!-- Switch Track (Background) -->
        <div
          class="absolute inset-0 transition-colors duration-200 rounded-full"
          [ngClass]="{
            'bg-sa-600': isSwitchOn() && !disabled,
            'bg-gray-300': !isSwitchOn() && !disabled,
            'border-2 border-error-500': formSubmitted && control?.errors
          }"
        ></div>

        <!-- Switch Thumb (Circle) -->
        <div
          class="absolute top-0.5 left-0.5 bg-white rounded-full shadow-sm transition-transform duration-200"
          [ngClass]="{
            'w-4 h-4': switchSize === 'sm',
            'w-5 h-5': switchSize === 'md',
            'w-6 h-6': switchSize === 'lg',
            'translate-x-5': isSwitchOn() && switchSize === 'sm',
            'translate-x-6': isSwitchOn() && switchSize === 'md',
            'translate-x-7': isSwitchOn() && switchSize === 'lg'
          }"
        ></div>
      </label>
    </div>

    <!-- Label and Description Container -->
    <div class="flex-1 min-w-0 text-left rtl:text-right">
      <label
        [attr.for]="name"
        class="block text-sm font-medium text-gray-900 cursor-pointer"
        [ngClass]="{
          'text-error-600': formSubmitted && control?.errors
        }"
        (click)="toggleSwitch()"
      >
        {{ label }}<span *ngIf="required" class="ml-1 text-error-500">*</span>
      </label>
      <p *ngIf="description" class="mt-1 text-sm text-gray-600">{{ description }}</p>

      <!-- Switch Error Messages -->
      <div *ngIf="formSubmitted && control?.errors" class="mt-2">
        <div
          *ngIf="control?.errors?.['required'] || control?.errors?.['requiredTrue']"
          class="flex items-center text-xs text-error-600"
        >
          <svg class="flex-shrink-0 w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span>This selection is required</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Regular Input Label (Non-Checkbox, Non-Radio, Non-Switch) -->
  <label
    *ngIf="type !== 'checkbox' && type !== 'radio' && type !== 'switch'"
    [attr.for]="name"
    class="block text-sm font-medium text-gray-700 transition-colors duration-200"
    [ngClass]="{
      'text-error-600': formSubmitted && control?.errors
    }"
    >{{ label }}<span *ngIf="required" class="ml-1 rtl:ml-0 rtl:mr-1 text-error-500">*</span></label
  >
  <!-- Date Input or Date Range Input -->
  <div *ngIf="type === 'date' || isDateRange" class="relative">
    <input
      type="text"
      [placeholder]="isDateRange ? 'Select date range' : 'Select date'"
      [class]="class"
      [required]="required"
      [disabled]="disabled"
      [readonly]="true"
      [attr.name]="name"
      [value]="getDisplayValue()"
      class="w-full px-3 py-3 text-sm placeholder-gray-400 transition-all duration-300 ease-in-out bg-white border border-gray-400 rounded-md cursor-pointer focus:outline-none focus:border-gray-400 hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-400 focus:border-gray-400'"
      (click)="openDatePicker(); $event.stopPropagation()"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />



    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-[97%]': isFocused || isDatePickerOpen,
        'w-0': !isFocused && !isDatePickerOpen,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>

    <!-- Date Picker Component -->
    <sdga-date-picker
      [isOpen]="isDatePickerOpen"
      [selectedDate]="selectedDate"
      [selectedRange]="selectedRange"
      [isRange]="isDateRange"
      [minDate]="minDate"
      [maxDate]="maxDate"
      (dateSelected)="onDateSelected($event)"
      (rangeSelected)="onRangeSelected($event)"
      (closed)="closeDatePicker()"
    ></sdga-date-picker>
  </div>

  <!-- Search Inputs -->
  <div *ngIf="type === 'search'" class="relative">
    <!-- Search Icon -->
    <div *ngIf="showSearchIcon" class="absolute z-10 transform -translate-y-1/2 top-1/2"
         [style.left]="getSearchIconPosition()"
         [style.right]="getSearchIconRightPosition()">
      <sdga-svg-icon
        name="search"
        svgClass="w-5 h-5 text-gray-400">
      </sdga-svg-icon>
    </div>

    <!-- Search Input -->
    <input
      [type]="type"
      [placeholder]="placeholder || label"
      [class]="class"
      [required]="required"
      [disabled]="disabled"
      [readonly]="isReadOnly || readonly"
      [attr.name]="name"
      [value]="value"
      class="w-full py-3 text-sm placeholder-gray-400 transition-all duration-300 ease-in-out bg-white border border-gray-300 rounded-md focus:outline-none focus:border-gray-400 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100"
      [ngClass]="{
        'pl-10 pr-12': showSearchIcon && enableVoiceSearch,
        'pl-10 pr-3': showSearchIcon && !enableVoiceSearch,
        'pl-3 pr-12': !showSearchIcon && enableVoiceSearch,
        'px-3': !showSearchIcon && !enableVoiceSearch,
        'border-error-500 bg-error-25 focus:border-error-500': formSubmitted && control?.errors,
        'border-gray-300': !(formSubmitted && control?.errors)
      }"
      [style.padding-left]="getLeftPadding()"
      [style.padding-right]="getRightPadding()"
      (change)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />
    <!-- Voice Search Button -->
    <button
      *ngIf="enableVoiceSearch"
      type="button"
      class="absolute z-10 p-1 transition-colors duration-200 transform -translate-y-1/2 rounded-md top-1/2 hover:bg-gray-100 focus:outline-none focus:ring-0"
      [style.left]="getVoiceIconPosition()"
      [style.right]="getVoiceIconRightPosition()"
      [ngClass]="{
        'text-red-500': isListening,
        'text-gray-400 hover:text-gray-500': !isListening
      }"
      [title]="isListening ? 'Stop voice search' : 'Start voice search'"
      (click)="isListening ? stopVoiceSearch() : startVoiceSearch()"
      [disabled]="disabled"
    >
      <sdga-svg-icon
        name="mic"
        svgClass="w-5 h-5"
        [ngClass]="{
          'text-red-500 animate-pulse': isListening,
          'text-gray-400': !isListening
        }">
      </sdga-svg-icon>
    </button>

    <!-- Animated border bottom (same as text inputs) -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-[97%]': isFocused,
        'w-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <!-- Regular Text Inputs -->
  <div *ngIf="type != 'textarea' && type != 'number' && type !== 'checkbox' && type !== 'radio' && type !== 'switch' && type !== 'date' && type !== 'search' && !isDateRange" class="relative">
    <input
      [type]="type"
      [placeholder]="placeholder || label"
      [class]="class"
      [required]="required"
      [disabled]="disabled"
      [readonly]="isReadOnly || readonly"
      [attr.name]="name"
      [value]="value"
      [min]="min"
      [max]="max"
      (keydown)="preventIfDate($event)"
      class="w-full px-3 py-3 text-sm placeholder-gray-400 transition-all duration-300 ease-in-out bg-white border border-gray-300 rounded-md focus:outline-none focus:border-gray-400 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-300 focus:border-sa-500'"
      (change)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />
    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-[97%]': isFocused,
        'w-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>



  <!-- Decimal Input -->
  <div *ngIf="type === 'number' && step === 'any'" class="relative">
    <div class="relative flex items-center ltr:flex-row rtl:flex-row-reverse" style="direction: ltr;">
      <!-- Decrement Button -->
      <button
        type="button"
        class="flex items-center justify-center order-1 w-12 h-12 text-gray-600 transition-all duration-200 bg-white border border-r-0 border-gray-400 hover:bg-gray-50 hover:border-gray-400 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 rounded-l-md rtl:rounded-r-none rtl:rounded-l-md rtl:border-l rtl:border-r-0 rtl:order-3"
        [ngClass]="{
          'border-error-500 bg-red-50 text-red-600': formSubmitted && control?.errors,
          'border-gray-400': !(formSubmitted && control?.errors)
        }"
        [disabled]="disabled || isReadOnly || readonly"
        (click)="decrementNumber()"
        [attr.aria-label]="'ACCESSIBILITY.DECREASE_VALUE' | translate"
        tabindex="0"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
        </svg>
      </button>

      <!-- Number Input -->
      <input
        [type]="type"
        [placeholder]="placeholder || label"
        [class]="class"
        [required]="required"
        [disabled]="disabled"
        [readonly]="isReadOnly || readonly"
        [attr.name]="name"
        [value]="value"
        [min]="min"
        [max]="max"
        [attr.step]="step"
        [attr.aria-label]="label"
        inputmode="decimal"
        (keydown)="preventIfDate($event)"
        class="flex-1 px-3 py-3 text-sm text-center placeholder-gray-400 transition-all duration-300 ease-in-out h-12 bg-white border-t border-b border-gray-400 focus:outline-none focus:border-sa-500 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none order-2"
        [ngClass]="formSubmitted && control?.errors
          ? 'border-error-500 bg-red-50 focus:border-error-500'
          : 'border-gray-400 focus:border-gray-400'"
        (change)="onInputChange($event)"
        (focus)="onFocus()"
        (blur)="onBlur()"
      />

      <!-- Increment Button -->
      <button
        type="button"
        class="flex items-center justify-center order-3 w-12 h-12 text-gray-600 transition-all duration-200 bg-white border border-l-0 border-gray-400 hover:bg-gray-50 hover:border-gray-400 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 rounded-r-md rtl:rounded-l-none rtl:rounded-r-md rtl:border-l-0 rtl:order-1"
        [ngClass]="{
          'border-error-500 bg-red-50 text-red-600': formSubmitted && control?.errors,
          'border-gray-400': !(formSubmitted && control?.errors)
        }"
        [disabled]="disabled || isReadOnly || readonly"
        (click)="incrementNumber()"
        [attr.aria-label]="'ACCESSIBILITY.INCREASE_VALUE' | translate"
        tabindex="0"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
      </button>
    </div>

    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-3 right-3 h-0.5 rounded-full transition-all duration-300 ease-in-out origin-center"
      [ngClass]="{
        'scale-x-100': isFocused,
        'scale-x-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <!-- Integer Input -->
  <div *ngIf="type === 'number' && step !== 'any'" class="relative">
    <div class="relative flex items-center ltr:flex-row rtl:flex-row-reverse" style="direction: ltr;">
      <!-- Decrement Button -->
      <button
        type="button"
        class="flex items-center justify-center order-1 w-12 h-12 text-gray-600 transition-all duration-200 bg-white border border-r-0 border-gray-400 hover:bg-gray-50 hover:border-gray-400 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 rounded-l-md rtl:rounded-r-none rtl:rounded-l-md rtl:border-l rtl:border-r-0 rtl:order-3"
        [ngClass]="{
          'border-error-500 bg-red-50 text-red-600': formSubmitted && control?.errors,
          'border-gray-400': !(formSubmitted && control?.errors)
        }"
        [disabled]="disabled || isReadOnly || readonly"
        (click)="decrementNumber()"
        [attr.aria-label]="'ACCESSIBILITY.DECREASE_VALUE' | translate"
        tabindex="0"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
        </svg>
      </button>

      <!-- Number Input -->
      <input
        [type]="type"
        [placeholder]="placeholder || label"
        [class]="class"
        [required]="required"
        [disabled]="disabled"
        [readonly]="isReadOnly || readonly"
        [attr.name]="name"
        [value]="value"
        [min]="min"
        [max]="max"
        [attr.step]="step"
        [attr.aria-label]="label"
        inputmode="numeric"
        pattern="[0-9]*"
        onkeydown="if(event.key==='.'){event.preventDefault();}"
        oninput="event.target.value = event.target.value.replace(/[^0-9]*/g,'');"
        class="flex-1 px-3 py-3 text-sm text-center placeholder-gray-400 transition-all duration-300 ease-in-out h-12 bg-white border-t border-b border-gray-400 focus:outline-none focus:border-sa-500 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none order-2"
        [ngClass]="formSubmitted && control?.errors
          ? 'border-error-500 bg-red-50 focus:border-error-500'
          : 'border-gray-400 focus:border-gray-400'"
        (change)="onInputChange($event)"
        (focus)="onFocus()"
        (blur)="onBlur()"
      />

      <!-- Increment Button -->
      <button
        type="button"
        class="flex items-center justify-center order-3 w-12 h-12 text-gray-600 transition-all duration-200 bg-white border border-l-0 border-gray-400 hover:bg-gray-50 hover:border-gray-400 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 rounded-r-md rtl:rounded-l-none rtl:rounded-r-md rtl:border-l-0 rtl:order-1"
        [ngClass]="{
          'border-error-500 bg-red-50 text-red-600': formSubmitted && control?.errors,
          'border-gray-400': !(formSubmitted && control?.errors)
        }"
        [disabled]="disabled || isReadOnly || readonly"
        (click)="incrementNumber()"
        [attr.aria-label]="'ACCESSIBILITY.INCREASE_VALUE' | translate"
        tabindex="0"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
      </button>
    </div>

    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-3 right-3 h-0.5 rounded-full transition-all duration-300 ease-in-out origin-center"
      [ngClass]="{
        'scale-x-100': isFocused,
        'scale-x-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <div *ngIf="type === 'textarea'" class="relative">
    <textarea
      [required]="required"
      [disabled]="disabled"
      [attr.name]="name"
      [value]="value"
      [readonly]="readonly || isReadOnly"
      [placeholder]="placeholder || label"
      rows="4"
      class="w-full px-3 py-3 border border-gray-300 rounded-md bg-white text-sm placeholder-gray-400 transition-all duration-300 ease-in-out focus:outline-none focus:border-sa-500 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 min-h-[100px] resize-y"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-300 focus:border-sa-500'"
      (change)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    ></textarea>
    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-full': isFocused,
        'w-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <!-- Error Messages (Non-Checkbox, Non-Radio, Non-Switch) -->
  <div *ngIf="type !== 'checkbox' && type !== 'radio' && type !== 'switch'" class="mt-1 space-y-1">
    <div
      *ngIf="formSubmitted && control?.errors?.['required']"
      class="flex items-center text-xs text-error-600"
    >
      <svg class="flex-shrink-0 w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>This field is required</span>
    </div>

    <div
      *ngIf="formSubmitted && control?.errors?.['maxlength']"
      class="flex items-center text-xs text-error-600"
    >
      <svg class="flex-shrink-0 w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>Maximum length exceeded</span>
    </div>

    <div
      *ngIf="formSubmitted && control?.errors?.['pattern']"
      class="flex items-center text-xs text-error-600"
    >
      <svg class="flex-shrink-0 w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>Invalid format</span>
    </div>
  </div>
</div>
