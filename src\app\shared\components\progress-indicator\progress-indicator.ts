import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, TemplateRef } from '@angular/core';
import { SvgIcon } from "../svg-icon/svg-icon";
import { TranslateModule } from '@ngx-translate/core';

export interface StepItem {
  title: string;
  content: TemplateRef<any>; // <-- Accept rich content
}
@Component({
  selector: 'sdga-progress-indicator',
  imports: [CommonModule, SvgIcon , TranslateModule],
  templateUrl: './progress-indicator.html',
  styleUrl: './progress-indicator.scss'
})
export class ProgressIndicator {
    @Input() steps: StepItem[] = [];
  @Input() currentStep = 0;

  @Output() stepChange = new EventEmitter<number>();

  next() {
    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++;
      this.stepChange.emit(this.currentStep);
    }
  }

  back() {
    if (this.currentStep > 0) {
      this.currentStep--;
      this.stepChange.emit(this.currentStep);
    }
  }
}
