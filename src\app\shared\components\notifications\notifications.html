<div [ngClass]="containerClasses" *ngIf="isVertical">
  <!-- Icon -->
  <sdga-svg-icon [name]="iconName" [svgClass]="iconClass"></sdga-svg-icon>
  <!-- Content -->
  <div [ngClass]="contentClasses">
    <div [ngClass]="contentTitleClasse">{{ title }}</div>
    <div [ngClass]="TextContent">{{ content }}</div>
    <div [ngClass]="actionsClasses">
      <button
        *ngFor="let btn of buttons"
        (click)="btn.onClick()"
        class="py-2 px-3 font-medium text-default-100"
      >
        {{ btn.label }}
      </button>
    </div>
  </div>
  <!-- Close Button -->
  <div>
    <sdga-svg-icon
      [name]="ButtonClose"
      [svgClass]="closeIconClass"
    ></sdga-svg-icon>
  </div>
</div>
<div [ngClass]="containerClasses" *ngIf="!isVertical">
  <div class="flex justify-between">
    <div class="icon p-2">
      <sdga-svg-icon [name]="iconName" [svgClass]="iconClass"></sdga-svg-icon>
    </div>
    <div class="close flex items-center">
      <sdga-svg-icon
        [name]="ButtonClose"
        [svgClass]="closeIconClass"
      ></sdga-svg-icon>
    </div>
  </div>
  <div class="content">
    <div [ngClass]="contentTitleClasse">{{title}}</div>
    <div [ngClass]="TextContent">{{content}}</div>
    <div class="actions flex flex-col">
       <button
        *ngFor="let btn of buttons"
        (click)="btn.onClick()"
        class="py-2 px-3 font-medium text-default-100 bg-gray-100 my-2 rounded-sm"
      >
        {{ btn.label }}
      </button>
    </div>
  </div>
</div>
