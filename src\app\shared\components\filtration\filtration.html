
<div class="relative">
    <button class="button button--lg neutral me-5" (click)="toggleFilter()">
        <sdga-svg-icon name="filter" svgClass="me-1 flex items-center"></sdga-svg-icon>
        Filter
        <sdga-svg-icon name="arrow-down" svgClass="ms-1 flex items-center"></sdga-svg-icon>
    </button>
    <div class="flex gap-4">
        <span tabindex="0" class="button button--md primary">Item</span>
        <span tabindex="0" class="button button--md primary">Item</span>
        <span tabindex="0" class="button button--md primary">Item</span>
    </div>
    <div class="filter-content" *ngIf="isFilterOpened">
        <p>Filter Content</p>
    </div>
</div>