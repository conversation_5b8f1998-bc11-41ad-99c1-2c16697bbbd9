<footer class="px-4 py-10 space-y-10 text-white bg-success-900 sm:px-6 lg:px-8 w-full mt-auto flex flex-col">
  <!-- Top Section: Groups -->
  <div class="grid grid-cols-2 gap-6 pb-10 md:grid-cols-3 lg:grid-cols-6 ">
    <div *ngFor="let group of footerLinks">
      <h4 class="pb-2 mb-2 border-b text-text-md border-default-white-30">{{ group.label }}</h4>
      <ul class="space-y-1">
        <li *ngFor="let link of group.links">
          <a href="#" class="text-text-sm hover:underline">{{ link }}</a>
        </li>
      </ul>
    </div>

    <!-- Social Media -->
    <div class="flex flex-col col-span-2 gap-8 md:col-span-1">
      <div>
        <h4 class="mb-2 text-text-md">Social Media</h4>
        <div class="flex gap-2">
          <button class="p-1.5 border border-white rounded-sm">
            <sdga-svg-icon name="arrow-left-long"></sdga-svg-icon>
          </button>
          <button class="p-1.5 border border-white rounded-sm">
            <sdga-svg-icon name="arrow-left-long"></sdga-svg-icon>
          </button>
          <button class="p-1.5 border border-white rounded-sm">
            <sdga-svg-icon name="arrow-left-long"></sdga-svg-icon>
          </button>
        </div>
      </div>

      <!-- Accessibility Tools -->
      <div>
        <h4 class="mb-2 text-text-md">Accessibility Tools</h4>
        <div class="flex gap-2">
          <button class="p-1.5 border border-white rounded-sm">
            <sdga-svg-icon name="arrow-left-long"></sdga-svg-icon>
          </button>
          <button class="p-1.5 border border-white rounded-sm">
            <sdga-svg-icon name="arrow-left-long"></sdga-svg-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Middle Section: Links -->
  <div class="flex flex-wrap justify-center gap-4 text-white underline md:gap-8 md:justify-start text-text-sm">
    <a href="#" *ngFor="let link of bottomLinks">{{ link }}</a>
  </div>

  <!-- Bottom Section -->
  <div class="flex flex-col items-center justify-between gap-10 text-xs sm:flex-row ">
    <div class="text-center sm:text-left">
      <div class="font-bold text-text-sm">All Right Reserved For Digital Government Authority © 2024</div>
      <div class="flex gap-4 mt-2">
        <a href="#">Terms and Conditions</a>
        <a href="#">Privacy Policy</a>
      </div>
    </div>
    <div class="flex items-center gap-4">
      <img src="assets/images/LogoPlaceholder.png" alt="Platform Logo" class="h-auto " />
      <img src="assets/images/LogoPlaceholder.png" alt="Platform Logo" class="h-auto " />
    </div>
  </div>
</footer>
