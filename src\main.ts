import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { App } from './app/app';
import { TranslateService } from '@ngx-translate/core';
import { DirectionService } from './app/core/services/direction.service';

bootstrapApplication(App, appConfig).then((appRef) => {
  const injector = appRef.injector;
  const translate = injector.get(TranslateService);
  const direction = injector.get(DirectionService);

  // Detect preferred language (from localStorage or default to 'en')
  const lang = (localStorage.getItem('lang') || 'en') as 'en' | 'ar';

  // Apply translation and direction

  translate.use(lang);
  direction.setDirection(lang);
});
