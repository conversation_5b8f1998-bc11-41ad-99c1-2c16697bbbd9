
<div class="relative inline-block">
    <button
        #filterButton
        class="button button--lg neutral"
        (click)="toggleFilter()"
        [class.active]="isFilterOpened">
        <sdga-svg-icon name="filter" svgClass="me-1 flex items-center"></sdga-svg-icon>
        Filter
        <sdga-svg-icon
            name="arrow-down"
            svgClass="ms-1 flex items-center transition-transform duration-200"
            [class.rotate-180]="isFilterOpened">
        </sdga-svg-icon>
    </button>

    <div
        class="filter-dropdown"
        *ngIf="isFilterOpened"
        (click)="$event.stopPropagation()">
        <p>Filter Content</p>
    </div>
</div>