import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DirectionService } from '../../../core/services/direction.service';
import { SvgIcon } from '../svg-icon/svg-icon';
import { CommonModule } from '@angular/common';
interface MegaMenuSection {
  title: string;
  links: { label: string; href: string }[];
}
@Component({
  selector: 'sdga-navbar',
  imports: [SvgIcon, CommonModule],
  templateUrl: './navbar.html',
  styleUrl: './navbar.scss',
})
export class Navbar {
  megaMenu: MegaMenuSection[] = [
    {
      title: 'Section 1',
      links: [
        { label: 'Sub Link 1', href: '#' },
        { label: 'Sub Link 2', href: '#' },
        { label: 'Sub Link 3', href: '#' },
      ],
    },
    {
      title: 'Section 2',
      links: [
        { label: 'Sub Link A', href: '#' },
        { label: 'Sub Link B', href: '#' },
      ],
    },
    {
      title: 'More',
      links: [
        { label: 'Docs', href: '#' },
        { label: 'Support', href: '#' },
      ],
    },
  ];
  dropdownOpen = false;
  lang: string | undefined;
  showMobileLinks = false;
  showMobileActions = false;
  constructor(
    private translate: TranslateService,
    private dir: DirectionService
  ) {
    this.lang = this.translate.currentLang;
  }

  switchLang(lang: 'en' | 'ar') {
    this.translate.use(lang);
    this.dir.setDirection(lang);
    localStorage.setItem('lang', lang);
  }

  @ViewChild('dropdown', { static: false }) dropdownRef!: ElementRef;

  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent) {
    if (
      this.dropdownOpen &&
      this.dropdownRef &&
      !this.dropdownRef.nativeElement.contains(event.target)
    ) {
      this.dropdownOpen = false;
    }
  }

  toggleMobileLinks() {
    this.showMobileLinks = !this.showMobileLinks;
    this.showMobileActions = false; // Close other menu
  }

  toggleMobileActions() {
    this.showMobileActions = !this.showMobileActions;
    this.showMobileLinks = false; // Close other menu
  }
}
