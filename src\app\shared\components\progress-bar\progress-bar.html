<!-- Linear Progress -->
<div *ngIf="type === 'linear'" class="w-full space-y-1">
  <div class="flex items-center justify-between">
    <span class="text-text-md text-default-100" *ngIf="label">{{ label }}</span>
  </div>
  <div class="relative w-full overflow-hidden bg-gray-200 rounded-full" [ngClass]="barHeightClass">
    <div class="flex items-center h-full transition-all duration-300 rounded-full" [ngClass]="{
        'bg-success-700': status === 'success',
        'bg-error-700': status === 'error',
        'bg-gray-700': status === 'neutral'
      }" [style.width.%]="value">
      <span *ngIf="barHeightClass === 'h-4'" class="ml-auto mr-2 text-xs text-white" style="white-space:nowrap;" [style.position]="'absolute'" [style.right]="(100 - value) + '%'" [style.transform]="'translateY(-50%)'" [style.top]="'50%'" >{{ value }}%</span>
    </div>
  </div>
  <div class="flex">
    <div *ngIf="helpText" class="flex items-center gap-2 text-left text-text-md text-default-100">
      <span>
        <sdga-svg-icon
          [name]="status === 'success' ? 'success-green' : status === 'error' ? 'error-red' : 'question-in-circle'"
          [svgClass]="statusIconClass"
        ></sdga-svg-icon>
      </span>
      <span [ngClass]="{
        'text-success-700': status === 'success',
        'text-error-700': status === 'error',
        'text-gray-700': status === 'neutral'
      }">
        {{ status === 'success' ? (helpText || 'Success!') : status === 'error' ? (helpText || 'Error!') : (helpText || 'Info') }}
      </span>
    </div>
  </div>
</div>

<!-- Circular Progress -->
<div *ngIf="type === 'circular'" class="relative flex flex-col items-center justify-center" [ngClass]="{
  'w-20 h-20': size === 'sm',
  'w-28 h-28': size === 'md',
  'w-36 h-36': size === 'lg'
}">
  <svg class="w-full h-full" viewBox="0 0 36 36">
    <circle class="text-gray-200" stroke-width="3" fill="none" cx="18" cy="18" r="16" stroke="currentColor"></circle>
    <circle class="transition-all duration-500 ease-out" [ngClass]="{
        'bg-success-700': status === 'success',
        'bg-error-700': status === 'error',
        'bg-gray-700': status === 'neutral'
      }" stroke-width="3" fill="none" cx="18" cy="18" r="16" stroke-linecap="round"
      [attr.stroke-dasharray]="circumference" [attr.stroke-dashoffset]="circumference - (value / 100) * circumference"
      [attr.stroke]="status === 'success' ? '#059669' : status === 'error' ? '#dc2626' : '#334155'" transform="rotate(-90 18 18)"></circle>
  </svg>
  <div class="absolute inset-0 flex flex-col items-center justify-center text-xs font-semibold">
    <ng-container *ngIf="showStatusIcon">
      <sdga-svg-icon
        [name]="status === 'success' ? 'success-green' : status === 'error' ? 'error-red' : 'question-in-circle'"
        [svgClass]="statusIconClass"
      ></sdga-svg-icon>
      <div *ngIf="label" [ngClass]="{
        'text-success-700': status === 'success',
        'text-error-700': status === 'error',
        'text-gray-700': status === 'neutral'
      }" class="text-[10px] mt-0.5 ">{{ label }}</div>
    </ng-container>
    <ng-container *ngIf="!showStatusIcon">
      <div [ngClass]="{
        'text-success-700': status === 'success',
        'text-error-700': status === 'error',
        'text-gray-700': status === 'neutral'
      }" class="font-bold text-text-lg">{{ value }}%</div>
      <div *ngIf="label" [ngClass]="{
        'text-success-700': status === 'success',
        'text-error-700': status === 'error',
        'text-gray-700': status === 'neutral'
      }" class="text-[10px] mt-0.5">{{ label }}</div>
    </ng-container>
  </div>
</div>
