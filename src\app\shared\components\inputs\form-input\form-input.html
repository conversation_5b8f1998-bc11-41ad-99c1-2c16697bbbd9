<div
  class="w-full"
  [ngClass]="{
    'border border-dashed border-purple-300 rounded-lg p-4 bg-purple-25': type == 'checkbox',
    'space-y-1': type != 'checkbox'
  }"
>
  <!-- Checkbox Layout -->
  <div *ngIf="type === 'checkbox'" class="flex items-start space-x-3">
    <!-- Checkbox Input Container -->
    <div class="relative flex-shrink-0 mt-0.5">
      <input
        [type]="type"
        [placeholder]="translatedLabel"
        [class]="class"
        [required]="required"
        [disabled]="disabled"
        [readonly]="isReadOnly || readonly"
        [attr.name]="name"
        [value]="value"
        [checked]="value"
        [min]="min"
        [max]="max"
        (keydown)="preventIfDate($event)"
        class="appearance-none border-2 border-gray-400 rounded bg-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-sa-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer block"
        [ngClass]="{
          'h-4 w-4': !checkboxSize || checkboxSize === 'sm',
          'h-5 w-5': checkboxSize === 'md',
          'h-6 w-6': checkboxSize === 'lg',
          'border-sa-600 bg-sa-600': value,
          'border-gray-400 bg-white': !value,
          'border-error-500': formSubmitted && control?.errors
        }"
        (change)="onInputChange($event)"
        (blur)="onBlur()"
        (click)="
          type === 'checkbox' && unClickable ? $event.preventDefault() : null
        "
      />

      <!-- Checkbox checkmark icon - positioned absolutely over the input -->
      <svg
        *ngIf="value"
        class="absolute pointer-events-none text-white inset-0 m-auto"
        [ngClass]="{
          'w-2.5 h-2.5': !checkboxSize || checkboxSize === 'sm',
          'w-3 h-3': checkboxSize === 'md',
          'w-3.5 h-3.5': checkboxSize === 'lg'
        }"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
    </div>

    <!-- Label and Description Container -->
    <div class="flex-1 min-w-0">
      <label
        [attr.for]="name"
        class="block text-sm font-medium text-gray-900 cursor-pointer"
        [ngClass]="{
          'text-error-600': formSubmitted && control?.errors
        }"
      >
        {{ translatedLabel }}<span *ngIf="required" class="text-error-500 ml-1">*</span>
      </label>

      <!-- Description Text -->
      <p *ngIf="description" class="mt-1 text-sm text-gray-600">
        {{ description }}
      </p>

      <!-- Checkbox Error Messages -->
      <div *ngIf="formSubmitted && control?.errors" class="mt-2">
        <div
          *ngIf="control?.errors?.['required']"
          class="flex items-center text-xs text-error-600"
        >
          <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span>This selection is required</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Regular Input Label (Non-Checkbox) -->
  <label
    *ngIf="type !== 'checkbox'"
    [attr.for]="name"
    class="block text-sm font-medium text-gray-700 transition-colors duration-200"
    [ngClass]="{
      'text-error-600': formSubmitted && control?.errors
    }"
    >{{ translatedLabel }}<span *ngIf="required" class="text-error-500 ml-1">*</span></label
  >
  <!-- Date Input or Date Range Input -->
  <div *ngIf="type === 'date' || isDateRange" class="relative">
    <input
      type="text"
      [placeholder]="isDateRange ? 'Select date range' : 'Select date'"
      [class]="class"
      [required]="required"
      [disabled]="disabled"
      [readonly]="true"
      [attr.name]="name"
      [value]="getDisplayValue()"
      class="w-full px-3 py-3 border border-gray-400 rounded-md bg-white text-sm placeholder-gray-400 transition-all duration-300 ease-in-out focus:outline-none focus:border-gray-400 hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 cursor-pointer"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-400 focus:border-gray-400'"
      (click)="openDatePicker(); $event.stopPropagation()"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />



    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-[97%]': isFocused || isDatePickerOpen,
        'w-0': !isFocused && !isDatePickerOpen,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>

    <!-- Date Picker Component -->
    <sdga-date-picker
      [isOpen]="isDatePickerOpen"
      [selectedDate]="selectedDate"
      [selectedRange]="selectedRange"
      [isRange]="isDateRange"
      [minDate]="minDate"
      [maxDate]="maxDate"
      (dateSelected)="onDateSelected($event)"
      (rangeSelected)="onRangeSelected($event)"
      (closed)="closeDatePicker()"
    ></sdga-date-picker>
  </div>

  <!-- Regular Text Inputs -->
  <div *ngIf="type != 'textarea' && type != 'number' && type !== 'checkbox' && type !== 'date' && !isDateRange" class="relative">
    <input
      [type]="type"
      [placeholder]="translatedLabel"
      [class]="class"
      [required]="required"
      [disabled]="disabled"
      [readonly]="isReadOnly || readonly"
      [attr.name]="name"
      [value]="value"
      [min]="min"
      [max]="max"
      (keydown)="preventIfDate($event)"
      class="w-full px-3 py-3 border border-gray-300 rounded-md bg-white text-sm placeholder-gray-400 transition-all duration-300 ease-in-out focus:outline-none focus:border-gray-400 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-300 focus:border-sa-500'"
      (change)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />
    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-[97%]': isFocused,
        'w-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>



  <!-- Decimal Input -->
  <div *ngIf="type === 'number' && step === 'any'" class="relative">
    <input
      [type]="type"
      [placeholder]="translatedLabel"
      [class]="class"
      [required]="required"
      [disabled]="disabled"
      [readonly]="isReadOnly || readonly"
      [attr.name]="name"
      [value]="value"
      [min]="min"
      [max]="max"
      [attr.step]="step"
      inputmode="decimal"
      (keydown)="preventIfDate($event)"
      class="w-full px-3 py-3 border border-gray-300 rounded-md bg-white text-sm placeholder-gray-400 transition-all duration-300 ease-in-out focus:outline-none focus:border-sa-500 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-300 focus:border-sa-500'"
      (change)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()" />
    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-full': isFocused,
        'w-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <!-- Integer Input -->
  <div *ngIf="type === 'number' && step !== 'any'" class="relative">
    <input
      [type]="type"
      [placeholder]="translatedLabel"
      [class]="class"
      [required]="required"
      [disabled]="disabled"
      [readonly]="isReadOnly || readonly"
      [attr.name]="name"
      [value]="value"
      [min]="min"
      [max]="max"
      [attr.step]="step"
      inputmode="numeric"
      pattern="[0-9]*"
      onkeydown="if(event.key==='.'){event.preventDefault();}"
      oninput="event.target.value = event.target.value.replace(/[^0-9]*/g,'');"
      class="w-full px-3 py-3 border border-gray-300 rounded-md bg-white text-sm placeholder-gray-400 transition-all duration-300 ease-in-out focus:outline-none focus:border-sa-500 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-300 focus:border-sa-500'"
      (change)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
      />
    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-full': isFocused,
        'w-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <div *ngIf="type === 'textarea'" class="relative">
    <textarea
      [required]="required"
      [disabled]="disabled"
      [attr.name]="name"
      [value]="value"
      [readonly]="readonly || isReadOnly"
      [placeholder]="translatedLabel"
      rows="4"
      class="w-full px-3 py-3 border border-gray-300 rounded-md bg-white text-sm placeholder-gray-400 transition-all duration-300 ease-in-out focus:outline-none focus:border-sa-500 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50 min-h-[100px] resize-y"
      [ngClass]="formSubmitted && control?.errors
        ? 'border-error-500 bg-error-25 focus:border-error-500'
        : 'border-gray-300 focus:border-sa-500'"
      (change)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    ></textarea>
    <!-- Animated border bottom -->
    <div
      class="absolute bottom-0 left-1/2 h-0.5 rounded-full transition-all duration-300 ease-in-out transform -translate-x-1/2"
      [ngClass]="{
        'w-full': isFocused,
        'w-0': !isFocused,
        'bg-error-500': formSubmitted && control?.errors,
        'bg-gray-900': !(formSubmitted && control?.errors)
      }"
    ></div>
  </div>

  <!-- Error Messages (Non-Checkbox) -->
  <div *ngIf="type !== 'checkbox'" class="space-y-1 mt-1">
    <div
      *ngIf="formSubmitted && control?.errors?.['required']"
      class="flex items-center text-xs text-error-600"
    >
      <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>This field is required</span>
    </div>

    <div
      *ngIf="formSubmitted && control?.errors?.['maxlength']"
      class="flex items-center text-xs text-error-600"
    >
      <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>Maximum length exceeded</span>
    </div>

    <div
      *ngIf="formSubmitted && control?.errors?.['pattern']"
      class="flex items-center text-xs text-error-600"
    >
      <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span>Invalid format</span>
    </div>
  </div>
</div>
