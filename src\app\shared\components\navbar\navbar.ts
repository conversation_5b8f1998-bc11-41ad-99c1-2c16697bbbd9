import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DirectionService } from '../../../core/services/direction.service';

@Component({
  selector: 'sdga-navbar',
  imports: [],
  templateUrl: './navbar.html',
  styleUrl: './navbar.scss'
})
export class Navbar {
constructor(
  private translate: TranslateService,
  private dir: DirectionService
) {}

switchLang(lang: 'en' | 'ar') {
  this.translate.use(lang);
  this.dir.setDirection(lang);
  localStorage.setItem('lang', lang);
}
}
