<nav
  [attr.aria-label]="ariaLabel"
  class="flex items-center justify-center"
  [class]="getSizeClasses()"
  (click)="onNavClick($event)">

  <div class="flex items-center space-x-1 rtl:space-x-reverse">

    <!-- Pagination Items -->
    <ng-container *ngFor="let pageItem of pages; trackBy: trackByPage">

      <!-- Previous Button -->
      <button
        *ngIf="pageItem.type === 'prev'"
        type="button"
        [disabled]="pageItem.disabled"
        [attr.aria-label]="'Go to previous page'"
        (click)="onPageClick(pageItem)"
        [class]="getButtonSizeClasses()"
        class="inline-flex items-center justify-center text-gray-500 transition-all duration-200 bg-transparent border-0 hover:text-gray-700 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:text-gray-500">

        <!-- Previous Icon -->
         <sdga-svg-icon name="arrow-left" svgClass="ltr:inline rtl:hidden w-4 h-4"></sdga-svg-icon>
        <sdga-svg-icon name="arrow-right" svgClass="rtl:inline ltr:hidden w-4 h-4"></sdga-svg-icon>
      </button>

      <!-- Next Button -->
      <button
        *ngIf="pageItem.type === 'next'"
        type="button"
        [disabled]="pageItem.disabled"
        [attr.aria-label]="'Go to next page'"
        (click)="onPageClick(pageItem)"
        [class]="getButtonSizeClasses()"
        class="inline-flex items-center justify-center text-gray-500 transition-all duration-200 bg-transparent border-0 hover:text-gray-700 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:text-gray-500">

        <!-- Next Icon -->
       

        <sdga-svg-icon name="arrow-right" svgClass="ltr:inline rtl:hidden w-4 h-4"></sdga-svg-icon>
        <sdga-svg-icon name="arrow-left" svgClass="rtl:inline ltr:hidden w-4 h-4"></sdga-svg-icon>
      </button>

      <!-- Page Number Button -->
      <button
        *ngIf="pageItem.type === 'page'"
        type="button"
        [disabled]="pageItem.disabled"
        [attr.aria-label]="'Go to page ' + pageItem.page"
        [attr.aria-current]="pageItem.active ? 'page' : null"
        (click)="onPageClick(pageItem)"
        [class]="getButtonSizeClasses()"
        [ngClass]="{
          'text-gray-900 relative': pageItem.active,
          'text-gray-700 hover:text-gray-900': !pageItem.active && !pageItem.disabled,
          'cursor-not-allowed opacity-50': pageItem.disabled
        }"
        class="inline-flex items-center justify-center font-medium transition-all duration-200 bg-transparent border-0 focus:outline-none">
        {{ pageItem.label }}
        <!-- Active page underline -->
        <span
          *ngIf="pageItem.active"
          class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-[3px] bg-sa-500 rounded-full">
        </span>
      </button>

      <!-- Ellipsis Dropdown -->
      <div
        *ngIf="pageItem.type === 'ellipsis'"
        class="relative inline-block">

        <button
          type="button"
          (click)="toggleDropdown(pageItem); $event.stopPropagation()"
          [class]="getButtonSizeClasses()"
          class="inline-flex items-center justify-center font-medium text-gray-400 transition-all duration-200 bg-white border border-gray-300 rounded hover:text-gray-600 hover:border-gray-400 focus:outline-none">
          {{ pageItem.label }}
        </button>

        <!-- Dropdown Menu -->
        <div
          *ngIf="pageItem.showDropdown"
          (click)="$event.stopPropagation()"
          class="absolute z-50 mt-1 overflow-y-auto bg-white border border-gray-300 rounded shadow-lg max-h-48"
          [ngClass]="{
            'left-0': !isRTL(),
            'right-0': isRTL()
          }"
          style="min-width: 120px;">

          <div class="py-1">
            <button
              *ngFor="let page of getDropdownPages(pageItem)"
              type="button"
              (click)="onDropdownPageClick(page)"
             
              class="flex items-center justify-between w-full px-3 py-2 text-sm text-black transition-colors duration-150 bg-white hover:bg-gray-100 hover:text-gray-900 ltr:text-left rtl:text-right">

              <!-- Page number and icon container -->
              <div class="flex items-center justify-between w-full ltr:flex-row rtl:flex-row-reverse">
                <!-- Page number -->
                <span class="ltr:mr-2 rtl:ml-2">{{ page }}</span>

                <!-- Active page indicator icon -->
                <sdga-svg-icon
                  *ngIf="page === currentPage"
                  name="check-mark"
                  svgClass="w-4 h-4 text-gray-900 ltr:ml-auto rtl:mr-auto">
                </sdga-svg-icon>
              </div>
            </button>
          </div>
        </div>
      </div>

    </ng-container>

  </div>
</nav>
