<p>modal-test works!</p>
<div class="grid grid-cols-4 gap-4 p-6 bg-gray-200">
  <div class="col-span-2">
    <sdga-modal
      [title]="'Title goes here'"
      [content]="'When a Modal needs a further detailed explanation, it goes here.'"
      [isVertical]="false"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"

    >
    </sdga-modal>
  </div>
  <div class="col-span-2">
    <sdga-modal
      [title]="'Information'"
      [content]="'This is a dynamic message.'"
      [isVertical]="false"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"
      [buttons]="[
    { type: 'outline' ,label: 'Confirm', onClick: onConfirm },
    {type: 'outline' , label: 'Cancel', onClick: onCancel }
  ]"
    >
    </sdga-modal>
  </div>

  <div class="col-span-2">
    <sdga-modal
      [title]="'Information'"
      [content]="'This is a dynamic message.'"
      [isVertical]="true"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"
      [buttons]="[
    {type: 'outline' , label: 'Confirm', onClick: onConfirm },
    { type: 'outline' ,label: 'Cancel', onClick: onCancel }
  ]"
    >
    </sdga-modal>
  </div>

  <div class="col-span-2">
    <sdga-modal
      [title]="'Information'"
      [content]="'This is a dynamic message.'"
      [isVertical]="true"
      [iconName]="'successIcon'"
      [ButtonClose]="'ButtonClose'"
      [buttons]="[
    {type: 'outline' , label: 'Confirm', onClick: onConfirm },
    { type: 'outline' ,label: 'Cancel', onClick: onCancel }
  ]"
    >
    </sdga-modal>
  </div>
</div>
