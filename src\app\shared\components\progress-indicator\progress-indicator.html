<div class="w-full max-w-xl mx-auto rounded-xl">
  <ol class="relative m-6 border-gray-300 ltr:border-l-2 rtl:border-r-2 ltr:border-l-gray-300 rtl:border-r-gray-300">
    <li *ngFor="let step of steps; let i = index" class="mb-10 ltr:ml-6 rtl:mr-6">
       <!-- Step number circle -->
    <span
      class="absolute flex items-center justify-center w-8 h-8 text-xs font-medium border rounded-full ltr:-left-4 rtl:-right-4"
      [ngClass]="{
        'bg-sa-600 text-white border-sa-600 border-2': i < currentStep,
        'border-sa-600 text-sa-600 !bg-white border-2': i === currentStep,
        'border-gray-300 text-gray-300 bg-white': i > currentStep
      }">
      <ng-container *ngIf="i < currentStep; else stepNumber">
        <sdga-svg-icon name="correct" svgClass="text-white"></sdga-svg-icon>
      </ng-container>
      <ng-template #stepNumber>
        {{ i + 1 }}
      </ng-template>
    </span>

    <h3
      class="font-medium leading-tight"
      [ngClass]="{
        'text-sa-600': i === currentStep,
        'text-gray-900': i < currentStep,
        'text-gray-300': i > currentStep
      }">
      {{ step.title }}
    </h3>
      <ng-container *ngIf="i === currentStep">
        <ng-container *ngTemplateOutlet="step.content"></ng-container>
      </ng-container>
    </li>
  </ol>

  <!-- Navigation Buttons -->
  <div class="flex justify-start space-x-6 rtl:space-x-reverse">
    <button
      class="px-4 py-2 text-black bg-white border-2 border-gray-300 rounded hover:bg-gray-300 disabled:opacity-50"
      (click)="back()" [disabled]="currentStep === 0">
            {{ 'GENERAL.BACK' | translate }}

    </button>

    <button
      class="px-4 py-2 text-white bg-green-600 rounded hover:bg-green-700 disabled:opacity-50"
      (click)="next()" [disabled]="currentStep === steps.length - 1">
      {{ 'GENERAL.NEXT' | translate }}
    </button>
  </div>
</div>
