/* _card.scss */

.card {
  @apply bg-white rounded-lg p-xl border border-gray-300 shadow-md text-default-100;
}

/* Size Modifiers */
.card--lg {
  @apply p-lg;
}

.card--lg {
  @apply p-6 shadow-lg;
}

/* Style Variants */
.card--flat {
  @apply border-0 shadow-none;
}

.card--primary {
  @apply border-sa-600 bg-sa-50;
}

.card--danger {
  @apply border-red-500 bg-red-50;
}

.card-disabled {
@apply bg-gray-200
}

.card-border-black {
  @apply border-default-100 border-2
}
