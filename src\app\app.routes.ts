import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./layouts/default-layout/default-layout').then(
        (m) => m.DefaultLayout
      ),
    children: [
      {
        path: 'examples',
        data: { breadcrumb: 'Examples' },
        children: [
          {
            path: 'tailwind-calsses',
            data: { breadcrumb: 'Tailwind Classes' },
            loadComponent: () =>
              import('./examples/tailwind-classes-test/tailwind-classes-test').then(
                (m) => m.TailwindClassesTest
              ),
          },
          {
            path: 'svg-icons',
            data: { breadcrumb: 'SVG Icons' },
            loadComponent: () =>
              import('./examples/svg-icon-test/svg-icon-test').then(
                (m) => m.SvgIconTest
              ),
          },
          {
            path: 'progress-indicators',
            data: { breadcrumb: 'Progress Indicators' },
            loadComponent: () =>
              import('./examples/progress-indicator-test/progress-indicator-test').then(
                (m) => m.ProgressIndicatorTest
              ),
          },
          {
            path: 'inputs',
            data: { breadcrumb: 'Inputs' },
            loadComponent: () =>
              import('./examples/inputs-test/inputs-test').then(
                (m) => m.InputsTest
              ),
          },
          {
            path: 'accordion',
            data: { breadcrumb: 'Accordion' },
            loadComponent: () =>
              import('./examples/accordion-test/accordion-test').then(
                (m) => m.AccordionTest
              ),
          },

           {
            path: 'notifications',
            data: { breadcrumb: 'Notifications' },
            loadComponent: () =>
              import('./examples/notification-toast-test/notification-toast-test').then(
                (m) => m.NotificationToastTest
              ),
          },


            {
            path: 'rating',
            data: { breadcrumb: 'rating' },
            loadComponent: () =>
              import('./examples/rating-test/rating-test').then(
                (m) => m.RatingTest
              ),
          },

              {
            path: 'modal',
            data: { breadcrumb: 'modal' },
            loadComponent: () =>
              import('./examples/modal-test/modal-test').then(
                (m) => m.ModalTest
              ),
          },

             {
            path: 'quote',
            data: { breadcrumb: 'quote' },
            loadComponent: () =>
              import('./examples/quote-test/quote-test').then(
                (m) => m.QuoteTest
              ),
          },

          {
            path: 'buttons',
            data: { breadcrumb: 'Buttons' },
            loadComponent: () =>
              import('./examples/buttons-test/buttons-test').then(
                (m) => m.ButtonsTest
              ),
          },
          {
            path: 'content-switcher',
            data: { breadcrumb: 'Content Switcher' },
            loadComponent: () =>
              import('./examples/content-switcher-test/content-switcher-test').then(
                (m) => m.ContentSwitcherTest
              ),
          },
          {
            path: 'tages',
            data: { breadcrumb: 'Tages' },
            loadComponent: () =>
              import('./examples/tages-test/tages-test').then(
                (m) => m.TagesTest
              ),
          },
          {
            path: 'menu',
            data: { breadcrumb: 'menu' },
            loadComponent: () =>
              import('./examples/menu-test/menu-test').then(
                (m) => m.MenuTest
              ),
          },
          {
            path: 'progress-bar',
            data: { breadcrumb: 'Progress Bar' },
            loadComponent: () =>
              import('./examples/progress-bar-test/progress-bar-test').then(
                (m) => m.ProgressBarTest
              ),
          },
          {
             path: 'tree-example',
            data: { breadcrumb: 'Tree Example' },
            loadComponent: () =>
              import('./examples/tree-example/tree-example').then(
                (m) => m.TreeExample
              ),
          },
          {
             path: 'filtration',
            data: { breadcrumb: 'Filtration' },
            loadComponent: () =>
              import('./examples/filtration-test/filtration-test').then(
                (m) => m.FiltrationTest
              ),
          },
          {
             path: 'avatar',
            data: { breadcrumb: 'Avatar' },
            loadComponent: () =>
              import('./examples/avatar-test/avatar-test').then(
                (m) => m.AvatarTest
              ),
          },
        ],
      },
      {
        path: '',
        redirectTo: '',
        pathMatch: 'full'
      }
    ],
  },
];
