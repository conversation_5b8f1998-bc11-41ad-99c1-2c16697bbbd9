import { Component } from '@angular/core';
import {
  ActivatedRoute,
  ActivatedRouteSnapshot,
  NavigationEnd,
  Router,
  RouterModule
} from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SvgIcon } from '../svg-icon/svg-icon';
import { filter } from 'rxjs';

@Component({
  selector: 'sdga-breadcrumb',
  standalone: true,
  imports: [CommonModule, TranslateModule, RouterModule, SvgIcon],
  templateUrl: './breadcrumb.html',
  styleUrl: './breadcrumb.scss'
})
export class Breadcrumb {
  breadcrumbs: { label: string; url: string }[] = [];

  constructor(private router: Router, private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        const root = this.route.snapshot.root;
        this.breadcrumbs = this.getBreadcrumbs(root);
        // Remove this debug log in production
        // console.log('Breadcrumbs:', this.breadcrumbs);
      });
    // Also set breadcrumbs on first load
    const root = this.route.snapshot.root;
    this.breadcrumbs = this.getBreadcrumbs(root);
  }
getBreadcrumbs(
  route: ActivatedRouteSnapshot,
  url: string = '',
  breadcrumbs: { label: string; url: string }[] = []
): { label: string; url: string }[] {
  const children: ActivatedRouteSnapshot[] = route.children;

  for (const child of children) {
    const routeURL = child.url.map((segment) => segment.path).join('/');
    const nextUrl = routeURL ? (url ? `${url}/${routeURL}` : routeURL) : url;

    const label = child.data?.['breadcrumb'];
    if (label) {
      breadcrumbs.push({ label, url: '/' + nextUrl });
    }

    // ✅ Call recursively for deeper levels
    this.getBreadcrumbs(child, nextUrl, breadcrumbs);
  }

  return breadcrumbs;
}

}

