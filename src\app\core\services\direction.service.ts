import { Injectable, inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';

@Injectable({ providedIn: 'root' })
export class DirectionService {
  private doc = inject(DOCUMENT);

  setDirection(lang: 'en' | 'ar') {
    const html = this.doc.documentElement;
    const dir = lang === 'ar' ? 'rtl' : 'ltr';
    html.setAttribute('dir', dir);
    html.classList.remove('rtl', 'ltr');
    html.classList.add(dir);
  }
}
