<nav class="relative flex items-center justify-between w-full h-16 px-4 bg-white border-2 border-blue-100 sm:px-9">
  <!-- Left: Mobile Start Button (Three dots) -->
  <div class="absolute left-4 lg:hidden">
    <button class="text-gray-600 hover:text-black" (click)="toggleMobileLinks()">
      <sdga-svg-icon name="3dots"></sdga-svg-icon>
    </button>
  </div>

  <!-- Center: Logo -->
  <div class="absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2 lg:hidden">
    <a href="">
      <img src="assets/images/LogoPlaceholder.png" alt="Platform Logo" class="w-auto h-14" />
    </a>
  </div>

  <!-- Right: Mobile Menu Icon -->
  <div class="absolute right-4 lg:hidden" (click)="toggleMobileActions()">
    <button class="text-gray-600 hover:text-black">
      <sdga-svg-icon name="burger-menu"></sdga-svg-icon>

    </button>
  </div>

  <!-- Desktop: Full Layout -->
  <div class="items-center justify-between hidden w-full lg:flex">
    <!-- Left: Logo and Links -->
    <div class="flex items-center gap-6">
      <a href="">
        <img src="assets/images/LogoPlaceholder.png" alt="Platform Logo" class="w-auto" />
      </a>

      <ul class="flex items-center gap-6">
        <li class="relative" #dropdown>
          <a href="#" class="flex items-center gap-0.5 text-text-md text-default-100 hover:text-sa-600 font-semibold"
            (click)="toggleDropdown(); $event.stopPropagation()">
            <span>Link</span>
            <sdga-svg-icon name="arrow-down"></sdga-svg-icon>
          </a>

          <!-- ✅ Mega Menu Panel -->
          <div *ngIf="dropdownOpen" class="absolute left-0 z-50 w-screen max-w-5xl p-6 mt-2 bg-white shadow-lg">
            <div class="grid grid-cols-3 gap-6">
              <div *ngFor="let section of megaMenu">
                <h4 class="mb-2 text-sm font-semibold text-sa-600">{{ section.title }}</h4>
                <ul class="space-y-2">
                  <li *ngFor="let link of section.links">
                    <a href="{{ link.href }}" class="block text-sm text-default-600 hover:text-sa-600">
                      {{ link.label }}
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </li>
      </ul>

    </div>

    <!-- Right: Actions -->
    <div class="flex items-center gap-2">
      <!-- English Button -->
      <button
        class="relative flex items-center gap-1 px-4 sm:px-6 py-2 sm:py-6 md:py-2.5 lg:py-6 text-sm font-semibold text-white rounded-sm bg-sa-600 hover:bg-sa-700 focus:bg-sa-800 focus:border-2 focus:border-white focus:outline focus:outline-2 focus:outline-sa-800 disabled:bg-gray-400 group"
        (click)="switchLang('en')">
        <sdga-svg-icon name="square-arrow-left"></sdga-svg-icon>
        <span class="hidden sm:inline">En</span>
        <span
          class="absolute bottom-0 left-1/2 -translate-x-1/2 w-[80%] h-1.5 rounded-full bg-sa-400 opacity-0 transition-opacity group-hover:opacity-100 pointer-events-none"></span>
      </button>

      <!-- Arabic Button -->
      <button
        class="relative flex items-center gap-1 px-4 sm:px-6 py-2 sm:py-6 md:py-2.5 lg:py-6 text-sm font-semibold rounded-sm focus:border-2 text-default-100 hover:bg-gray-100 aria-pressed:bg-gray-800 focus:border-black group"
        (click)="switchLang('ar')">
        <sdga-svg-icon name="square-arrow-left"></sdga-svg-icon>
        <span class="hidden sm:inline">AR</span>
        <span
          class="absolute bottom-0 left-1/2 -translate-x-1/2 w-[80%] h-1.5 rounded-full bg-gray-400 opacity-0 transition-opacity group-hover:opacity-100 pointer-events-none"></span>
      </button>
    </div>
  </div>
</nav>

<!-- Mobile: Navigation Links Panel -->
<!-- Mobile: Navigation Links Panel -->
<div *ngIf="showMobileLinks" class="px-4 py-3 space-y-4 bg-white shadow lg:hidden">
  <div *ngFor="let section of megaMenu">
    <h4 class="mb-2 text-sm font-semibold text-sa-600">{{ section.title }}</h4>
    <ul class="space-y-2">
      <li *ngFor="let link of section.links">
        <a href="{{ link.href }}" class="block text-sm text-default-600 hover:text-sa-600">
          {{ link.label }}
        </a>
      </li>
    </ul>
  </div>
</div>

<!-- Mobile: Actions Panel -->
<div *ngIf="showMobileActions" class="px-4 py-3 space-y-2 bg-white shadow lg:hidden">
  <!-- Language Switchers (reusing same buttons from desktop) -->
  <button
    class="flex items-center justify-center w-full gap-2 px-4 py-2 text-sm font-semibold text-white rounded bg-sa-600 hover:bg-sa-700"
    (click)="switchLang('en')">
    <sdga-svg-icon name="square-arrow-left"></sdga-svg-icon>
    En
  </button>

  <button
    class="flex items-center justify-center w-full gap-2 px-4 py-2 text-sm font-semibold text-gray-800 border border-gray-300 rounded hover:bg-gray-100"
    (click)="switchLang('ar')">
    <sdga-svg-icon name="square-arrow-left"></sdga-svg-icon>
    AR
  </button>
</div>
