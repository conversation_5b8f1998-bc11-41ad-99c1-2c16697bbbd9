// Avatar component styles
:host {
  display: inline-block;
}

// Ensure proper image rendering
img {
  user-select: none;
  -webkit-user-drag: none;
}

// Smooth transitions for hover effects
.avatar-container {
  transition: all 0.2s ease-in-out;
}

// Group avatar overlapping styles
.avatar-group {
  .avatar-item {
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
      z-index: 999 !important;
    }
  }
}

// Custom focus styles for accessibility
.avatar-container:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}