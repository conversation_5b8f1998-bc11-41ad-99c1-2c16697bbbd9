<div class="container max-w-4xl p-6 mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="mb-4 text-3xl font-bold text-gray-900">{{ 'TREE_EXAMPLE.TITLE' | translate }}</h1>
    <p class="mb-6 text-gray-600">{{ 'TREE_EXAMPLE.DESCRIPTION' | translate }}</p>

    <!-- Language Switcher -->
    <div class="flex items-center mb-6 space-x-4 rtl:space-x-reverse">
      <span class="text-sm font-medium text-gray-700">{{ 'GLOBAL.CURRENT_LANGUAGE' | translate }}:</span>
      <button
        (click)="switchLanguage('en')"
        [class.bg-sa-600]="getCurrentLanguage() === 'en'"
        [class.text-white]="getCurrentLanguage() === 'en'"
        [class.bg-gray-200]="getCurrentLanguage() !== 'en'"
        [class.text-gray-700]="getCurrentLanguage() !== 'en'"
        class="px-3 py-1 text-sm font-medium transition-colors duration-200 rounded-md"
      >
        English
      </button>
      <button
        (click)="switchLanguage('ar')"
        [class.bg-sa-600]="getCurrentLanguage() === 'ar'"
        [class.text-white]="getCurrentLanguage() === 'ar'"
        [class.bg-gray-200]="getCurrentLanguage() !== 'ar'"
        [class.text-gray-700]="getCurrentLanguage() !== 'ar'"
        class="px-3 py-1 text-sm font-medium transition-colors duration-200 rounded-md"
      >
        العربية
      </button>
    </div>
  </div>

  <!-- Tree Component Example -->
  <div class="p-6 bg-white border border-gray-200 rounded-lg">
    <h2 class="mb-4 text-xl font-semibold text-gray-900">{{ 'TREE_EXAMPLE.SIMPLE_TREE' | translate }}</h2>
    <p class="mb-4 text-sm text-gray-600">{{ 'TREE_EXAMPLE.SIMPLE_TREE_DESC' | translate }}</p>

    <!-- Feature Highlights -->
    <div class="p-4 mb-6 border border-blue-200 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50">
      <h4 class="mb-2 font-semibold text-blue-900">🌟 Simple Content Sharing</h4>
      <ul class="space-y-1 text-sm text-blue-800">
        <li>• <strong>Easy Template Sharing:</strong> Use ng-template with #templateName</li>
        <li>• <strong>Simple Assignment:</strong> content: this.templateName</li>
        <li>• <strong>No Complex Context:</strong> Templates work independently</li>
        <li>• <strong>Flexible Content:</strong> Text, forms, components, anything!</li>
      </ul>
    </div>

    <!-- Tree Component -->
    <sdga-tree
      [nodes]="treeData"
      (nodeClick)="onNodeClick($event)"
    ></sdga-tree>
  </div>
</div>

<!-- Content Templates -->

<!-- Login Form Template -->
<ng-template #loginFormTpl>
  <div class="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ 'TREE_EXAMPLE.LOGIN_FORM_TITLE' | translate }}</h3>

    <form class="space-y-4">
      <div>
        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
          {{ 'TREE_EXAMPLE.USERNAME' | translate }}
        </label>
        <input
          type="text"
          id="username"
          name="username"
          class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sa-500 focus:border-sa-500"
          [placeholder]="'TREE_EXAMPLE.USERNAME_PLACEHOLDER' | translate"
        >
      </div>

      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
          {{ 'TREE_EXAMPLE.PASSWORD' | translate }}
        </label>
        <input
          type="password"
          id="password"
          name="password"
          class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sa-500 focus:border-sa-500"
          [placeholder]="'TREE_EXAMPLE.PASSWORD_PLACEHOLDER' | translate"
        >
      </div>

      <div class="flex items-center justify-between pt-4">
        <label class="flex items-center">
          <input type="checkbox" class="w-4 h-4 text-sa-600 border-gray-300 rounded focus:ring-sa-500">
          <span class="ml-2 text-sm text-gray-600">{{ 'TREE_EXAMPLE.REMEMBER_ME' | translate }}</span>
        </label>

        <button
          type="submit"
          class="px-6 py-3 text-sm font-medium text-white bg-sa-600 border border-transparent rounded-lg hover:bg-sa-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sa-500 transition-colors duration-200"
        >
          {{ 'TREE_EXAMPLE.LOGIN' | translate }}
        </button>
      </div>
    </form>
  </div>
</ng-template>

<!-- List 1 Template -->
<ng-template #list1Tpl>
  <div class="p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ 'TREE_EXAMPLE.LIST_1_TITLE' | translate }}</h3>

    <ul class="space-y-3">
      <li class="flex items-center p-3 bg-white rounded-lg shadow-sm border border-green-100">
        <div class="w-2 h-2 bg-green-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></div>
        <span class="text-gray-700">{{ 'TREE_EXAMPLE.LIST_1_ITEM_1' | translate }}</span>
      </li>
      <li class="flex items-center p-3 bg-white rounded-lg shadow-sm border border-green-100">
        <div class="w-2 h-2 bg-green-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></div>
        <span class="text-gray-700">{{ 'TREE_EXAMPLE.LIST_1_ITEM_2' | translate }}</span>
      </li>
      <li class="flex items-center p-3 bg-white rounded-lg shadow-sm border border-green-100">
        <div class="w-2 h-2 bg-green-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></div>
        <span class="text-gray-700">{{ 'TREE_EXAMPLE.LIST_1_ITEM_3' | translate }}</span>
      </li>
      <li class="flex items-center p-3 bg-white rounded-lg shadow-sm border border-green-100">
        <div class="w-2 h-2 bg-green-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></div>
        <span class="text-gray-700">{{ 'TREE_EXAMPLE.LIST_1_ITEM_4' | translate }}</span>
      </li>
    </ul>
  </div>
</ng-template>

<!-- List 2 Template -->
<ng-template #list2Tpl>
  <div class="p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ 'TREE_EXAMPLE.LIST_2_TITLE' | translate }}</h3>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="p-4 bg-white rounded-lg shadow-sm border border-purple-100">
        <h4 class="font-semibold text-purple-800 mb-2">{{ 'TREE_EXAMPLE.LIST_2_CATEGORY_1' | translate }}</h4>
        <ul class="space-y-2">
          <li class="text-sm text-gray-600">• {{ 'TREE_EXAMPLE.LIST_2_ITEM_1' | translate }}</li>
          <li class="text-sm text-gray-600">• {{ 'TREE_EXAMPLE.LIST_2_ITEM_2' | translate }}</li>
        </ul>
      </div>

      <div class="p-4 bg-white rounded-lg shadow-sm border border-purple-100">
        <h4 class="font-semibold text-purple-800 mb-2">{{ 'TREE_EXAMPLE.LIST_2_CATEGORY_2' | translate }}</h4>
        <ul class="space-y-2">
          <li class="text-sm text-gray-600">• {{ 'TREE_EXAMPLE.LIST_2_ITEM_3' | translate }}</li>
          <li class="text-sm text-gray-600">• {{ 'TREE_EXAMPLE.LIST_2_ITEM_4' | translate }}</li>
        </ul>
      </div>
    </div>
  </div>
</ng-template>

<!-- Text 1 Template -->
<ng-template #text1Tpl>
  <div class="p-6 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ 'TREE_EXAMPLE.TEXT_1_TITLE' | translate }}</h3>

    <div class="prose prose-sm max-w-none">
      <p class="text-gray-700 mb-4">{{ 'TREE_EXAMPLE.TEXT_1_PARAGRAPH_1' | translate }}</p>
      <p class="text-gray-700 mb-4">{{ 'TREE_EXAMPLE.TEXT_1_PARAGRAPH_2' | translate }}</p>

      <blockquote class="border-l-4 border-yellow-400 pl-4 italic text-gray-600 bg-yellow-25 p-3 rounded-r-lg">
        {{ 'TREE_EXAMPLE.TEXT_1_QUOTE' | translate }}
      </blockquote>

      <p class="text-gray-700 mt-4">{{ 'TREE_EXAMPLE.TEXT_1_PARAGRAPH_3' | translate }}</p>
    </div>
  </div>
</ng-template>

<!-- Text 2 Template -->
<ng-template #text2Tpl>
  <div class="p-6 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-lg border border-teal-200">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ 'TREE_EXAMPLE.TEXT_2_TITLE' | translate }}</h3>

    <div class="space-y-4">
      <div class="bg-white p-4 rounded-lg border border-teal-100">
        <h4 class="font-semibold text-teal-800 mb-2">{{ 'TREE_EXAMPLE.TEXT_2_SECTION_1' | translate }}</h4>
        <p class="text-gray-700 text-sm">{{ 'TREE_EXAMPLE.TEXT_2_CONTENT_1' | translate }}</p>
      </div>

      <div class="bg-white p-4 rounded-lg border border-teal-100">
        <h4 class="font-semibold text-teal-800 mb-2">{{ 'TREE_EXAMPLE.TEXT_2_SECTION_2' | translate }}</h4>
        <p class="text-gray-700 text-sm">{{ 'TREE_EXAMPLE.TEXT_2_CONTENT_2' | translate }}</p>
      </div>

      <div class="bg-white p-4 rounded-lg border border-teal-100">
        <h4 class="font-semibold text-teal-800 mb-2">{{ 'TREE_EXAMPLE.TEXT_2_SECTION_3' | translate }}</h4>
        <p class="text-gray-700 text-sm">{{ 'TREE_EXAMPLE.TEXT_2_CONTENT_3' | translate }}</p>
      </div>
    </div>
  </div>
</ng-template>