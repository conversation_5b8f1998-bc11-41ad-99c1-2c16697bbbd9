import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Breadcrumb } from "./shared/components/breadcrumb/breadcrumb";
import { TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, Breadcrumb, CommonModule],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements OnInit {
  protected title = 'sdga-theme';

  constructor(private translate: TranslateService) {}

  ngOnInit(): void {
    // Initialize translation service
    this.initializeTranslation();
  }

  private initializeTranslation(): void {
    // Set available languages
    this.translate.addLangs(['en', 'ar']);

    // Get stored language preference or default to 'en'
    const storedLanguage = localStorage.getItem('selectedLanguage');
    const defaultLanguage = storedLanguage || 'en';

    // Set default language
    this.translate.setDefaultLang('en');

    // Use the selected language
    this.translate.use(defaultLanguage);

    // Update document direction for RTL support
    this.updateDocumentDirection(defaultLanguage);
  }

  private updateDocumentDirection(language: string): void {
    const htmlElement = document.documentElement;

    if (language === 'ar') {
      htmlElement.setAttribute('dir', 'rtl');
      htmlElement.setAttribute('lang', 'ar');
    } else {
      htmlElement.setAttribute('dir', 'ltr');
      htmlElement.setAttribute('lang', 'en');
    }
  }
}
