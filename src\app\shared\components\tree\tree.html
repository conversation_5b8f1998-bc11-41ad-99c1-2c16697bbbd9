<div class="w-full">
  <!-- Tree Nodes -->
  <div class="space-y-1">
    <ng-container *ngFor="let node of nodes">
      <div class="w-full">
        <!-- Node Item - Clickable Title -->
        <div
          class="flex items-center p-3 transition-all duration-200 cursor-pointer hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-sa-500 focus:ring-offset-2"
          [class]="getNodeClasses(node)"
          [class.border-l-4]="isMainTitle(node) && isExpanded(node)"
          [class.border-sa-600]="isMainTitle(node) && isExpanded(node)"
          [class.rtl:border-l-0]="isMainTitle(node) && isExpanded(node)"
          [class.rtl:border-r-4]="isMainTitle(node) && isExpanded(node)"
          (click)="onNodeTitleClick(node)"
          [attr.aria-label]="hasChildren(node) ? (isExpanded(node) ? ('Collapse' | translate) : ('Expand' | translate)) : ''"
        >
          <!-- Node Label -->
          <span class="flex-1 text-sm font-medium text-gray-900"
                [class.font-semibold]="hasChildren(node)">
            {{ getNodeLabel(node) }}
          </span>
        </div>

        <!-- Children -->
        <div
          *ngIf="hasChildren(node) && isExpanded(node)"
          class="pl-4 mt-1 ml-6 border-l border-gray-200 rtl:ml-0 rtl:mr-6 rtl:border-l-0 rtl:border-r rtl:pl-0 rtl:pr-4"
        >
          <sdga-tree
            [nodes]="node.children!"
            [level]="level + 1"
            (nodeClick)="nodeClick.emit($event)"
          ></sdga-tree>
        </div>
      </div>
    </ng-container>
  </div>

  <!-- Selected Node Content Display -->
  <div
    *ngIf="selectedNodeId && hasSelectedContent()"
    class="p-4 mt-6 border bg-sa-25 border-sa-200"
  >
    <ng-container *ngTemplateOutlet="getSelectedContent()"></ng-container>
  </div>
</div>