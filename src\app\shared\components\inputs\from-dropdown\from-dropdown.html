<!-- SDGA Dropdown Component -->
<div class="w-full space-y-1">
  <!-- Label -->
  <label
    *ngIf="label"
    [attr.for]="name"
    class="block text-sm font-medium text-gray-700 rtl:text-right ltr:text-left"
    [ngClass]="{
      'text-red-600': hasError
    }"
  >
    {{ label }}
    <span *ngIf="required" class="ml-1 text-red-500 rtl:mr-1 rtl:ml-0">*</span>
  </label>

  <!-- Dropdown Container -->
  <div class="relative" #dropdownTrigger>
    <!-- Dropdown Trigger -->
    <button
      type="button"
      [class]="'relative w-full transition-all duration-300 ease-in-out bg-white border border-gray-400 rounded-md shadow-sm focus:outline-none focus:border-gray-400 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-100 ' + sizeClasses"
      [ngClass]="{
        'border-error-500 bg-red-50 focus:border-error-500': hasError,
        'border-gray-400 focus:border-gray-400': !hasError,
        'cursor-not-allowed': disabled || readonly
      }"
      [disabled]="disabled || readonly"
      [attr.aria-expanded]="dropdownOpen"
      [attr.aria-haspopup]="true"
      [attr.aria-label]="label"
      (click)="toggleDropdown()"
      (focus)="onFocus()"
      (blur)="onBlur()"
    >
      <!-- Loading State -->
      <div *ngIf="loading" class="flex items-center justify-between w-full">
        <span class="text-gray-400">{{ 'FORM.LOADING' | translate }}</span>
        <svg class="w-4 h-4 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <!-- Normal State -->
      <div *ngIf="!loading" class="flex items-center justify-between w-full">
        <!-- Selected Value Display -->
        <span
          class="block truncate"
          [ngClass]="{
            'text-gray-400': !selectedValues.length,
            'text-gray-900': selectedValues.length
          }"
        >
          {{ getDisplayText() }}
        </span>

        <!-- Icons Container -->
        <div class="flex items-center space-x-1 rtl:space-x-reverse">
          <!-- Clear Button -->
          <button
            *ngIf="clearable && selectedValues.length && !disabled && !readonly"
            type="button"
            class="p-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            (click)="clearSelection(); $event.stopPropagation()"
            [attr.aria-label]="'FORM.CLEAR_SELECTION' | translate"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>

          <!-- Dropdown Arrow -->
          <svg
            class="w-4 h-4 text-gray-400 transition-transform duration-200"
            [ngClass]="{ 'rotate-180': dropdownOpen }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </div>
    </button>

    <!-- Animated Border Bottom -->
    <div
      class="absolute bottom-0 left-3 right-3 h-0.5 rounded-full transition-all duration-300 ease-in-out origin-center"
      [ngClass]="{
        'scale-x-100': isFocused,
        'scale-x-0': !isFocused,
        'bg-error-500': hasError,
        'bg-gray-900': !hasError
      }"
    ></div>

    <!-- Dropdown Panel -->
    <div
      *ngIf="dropdownOpen"
      #dropdownPanel
      class="absolute z-50 w-full mt-1 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg max-h-60 focus:outline-none"
      [ngClass]="{
        'border-error-500': hasError,
        'border-gray-300': !hasError
      }"
      role="listbox"
      [attr.aria-label]="label"
    >
      <!-- Search Input -->
      <div *ngIf="searchable" class="p-2 border-b border-gray-200">
        <div class="relative">
          <input
            #searchInput
            type="text"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
            [placeholder]="'FORM.SEARCH' | translate"
            [(ngModel)]="searchTerm"
            (input)="onSearchChange($event)"
            (keydown.arrowdown)="$event.preventDefault(); navigateOptions(1)"
            (keydown.arrowup)="$event.preventDefault(); navigateOptions(-1)"
            (keydown.enter)="$event.preventDefault(); selectFocusedOption()"
            (keydown.escape)="closeDropdown()"
          />
          <svg class="absolute right-3 top-2.5 w-4 h-4 text-gray-400 rtl:right-auto rtl:left-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>

      <!-- No Results Message -->
      <div *ngIf="!loading && filteredOptions.length === 0 && filteredGroups.length === 0" class="px-3 py-2 text-sm text-center text-gray-500">
        {{ 'FORM.NO_OPTIONS_FOUND' | translate }}
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="px-3 py-2 text-sm text-center text-gray-500">
        <div class="flex items-center justify-center space-x-2">
          <svg class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>{{ 'FORM.LOADING' | translate }}</span>
        </div>
      </div>

      <!-- Options List -->
      <div *ngIf="!loading" class="py-1">
        <!-- Ungrouped Options -->
        <div *ngFor="let option of filteredOptions; let i = index">
          <div
            class="relative flex items-center px-3 py-2 text-sm cursor-pointer select-none hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
            [ngClass]="{
              'bg-gray-50': focusedOptionIndex === i,
              'bg-sa-50 text-sa-700': isOptionSelected(option),
              'text-gray-900': !isOptionSelected(option),
              'opacity-50 cursor-not-allowed': option.disabled
            }"
            [attr.role]="'option'"
            [attr.aria-selected]="isOptionSelected(option)"
            [attr.aria-disabled]="option.disabled"
            (click)="selectOption(option)"
            (mouseenter)="focusedOptionIndex = i"
          >
            <!-- Multi-select checkbox -->
            <div *ngIf="isMultiSelect" class="flex items-center mr-3 rtl:ml-3 rtl:mr-0">
              <div class="relative">
                <input
                  type="checkbox"
                  class="w-4 h-4 bg-white border-2 border-gray-400 rounded text-sa-600 focus:ring-sa-500 focus:ring-2"
                  [checked]="isOptionSelected(option)"
                  [disabled]="option.disabled"
                  readonly
                />
                <svg
                  *ngIf="isOptionSelected(option)"
                  class="absolute w-2 h-2 text-white pointer-events-none top-1 left-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
            </div>

            <!-- Option Icon -->
            <div *ngIf="option.icon" class="flex items-center mr-3 rtl:ml-3 rtl:mr-0">
              <i [class]="option.icon" class="w-4 h-4 text-gray-400"></i>
            </div>

            <!-- Option Content -->
            <div class="flex-1 min-w-0">
              <div class="font-medium truncate">{{ option.label }}</div>
              <div *ngIf="option.description" class="text-xs text-gray-500 truncate">{{ option.description }}</div>
            </div>

            <!-- Selected Indicator for Single Select -->
            <div *ngIf="!isMultiSelect && isOptionSelected(option)" class="flex items-center ml-3 rtl:mr-3 rtl:ml-0">
              <svg class="w-4 h-4 text-sa-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Grouped Options -->
        <div *ngFor="let group of filteredGroups; let groupIndex = index">
          <!-- Group Header -->
          <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase border-t border-gray-200 bg-gray-50 first:border-t-0">
            {{ group.label }}
          </div>

          <!-- Group Options -->
          <div *ngFor="let option of group.options; let optionIndex = index">
            <div
              class="relative flex items-center px-3 py-2 pl-6 text-sm cursor-pointer select-none hover:bg-gray-50 focus:bg-gray-50 focus:outline-none rtl:pr-6 rtl:pl-3"
              [ngClass]="{
                'bg-gray-50': focusedOptionIndex === (filteredOptions.length + groupIndex * 100 + optionIndex),
                'bg-sa-50 text-sa-700': isOptionSelected(option),
                'text-gray-900': !isOptionSelected(option),
                'opacity-50 cursor-not-allowed': option.disabled
              }"
              [attr.role]="'option'"
              [attr.aria-selected]="isOptionSelected(option)"
              [attr.aria-disabled]="option.disabled"
              (click)="selectOption(option)"
              (mouseenter)="focusedOptionIndex = filteredOptions.length + groupIndex * 100 + optionIndex"
            >
              <!-- Multi-select checkbox -->
              <div *ngIf="isMultiSelect" class="flex items-center mr-3 rtl:ml-3 rtl:mr-0">
                <div class="relative">
                  <input
                    type="checkbox"
                    class="w-4 h-4 bg-white border-2 border-gray-400 rounded text-sa-600 focus:ring-sa-500 focus:ring-2"
                    [checked]="isOptionSelected(option)"
                    [disabled]="option.disabled"
                    readonly
                  />
                  <svg
                    *ngIf="isOptionSelected(option)"
                    class="absolute w-2 h-2 text-white pointer-events-none top-1 left-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>

              <!-- Option Icon -->
              <div *ngIf="option.icon" class="flex items-center mr-3 rtl:ml-3 rtl:mr-0">
                <i [class]="option.icon" class="w-4 h-4 text-gray-400"></i>
              </div>

              <!-- Option Content -->
              <div class="flex-1 min-w-0">
                <div class="font-medium truncate">{{ option.label }}</div>
                <div *ngIf="option.description" class="text-xs text-gray-500 truncate">{{ option.description }}</div>
              </div>

              <!-- Selected Indicator for Single Select -->
              <div *ngIf="!isMultiSelect && isOptionSelected(option)" class="flex items-center ml-3 rtl:mr-3 rtl:ml-0">
                <svg class="w-4 h-4 text-sa-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Messages -->
  <div *ngIf="hasError" class="mt-1 text-sm text-red-600">
    <div *ngIf="control?.errors?.['required']" translate="FORM.IS_REQUIRED"></div>
    <div *ngIf="control?.errors?.['email']" translate="FORM.INVALID_EMAIL"></div>
    <div *ngIf="control?.errors?.['minlength']">
      {{ 'FORM.MIN_LENGTH' | translate: { min: control?.errors?.['minlength']?.requiredLength } }}
    </div>
    <div *ngIf="control?.errors?.['maxlength']">
      {{ 'FORM.MAX_LENGTH' | translate: { max: control?.errors?.['maxlength']?.requiredLength } }}
    </div>
    <div *ngIf="control?.errors?.['pattern']" translate="FORM.INVALID_FORMAT"></div>
  </div>

  <!-- Helper Text -->
  <div *ngIf="!hasError && placeholder" class="mt-1 text-sm text-gray-500">
    {{ placeholder }}
  </div>
</div>
